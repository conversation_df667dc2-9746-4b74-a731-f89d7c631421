<!DOCTYPE html>
<html lang="zh-CN">

<head>
 <link rel="shortcut icon" href="https://p1.meituan.net/dptakeaway/a12121edea8b4643e06a50cb728039954286.ico"/>
 <meta charset="UTF-8">
 <meta content="yes" name="apple-mobile-web-app-capable">
 <meta content="yes" name="apple-touch-fullscreen">
 <meta content="telephone=no,email=no" name="format-detection">
 <meta name="referrer" content="origin" />
 <meta name="viewport" content="width=device-width, initial-scale=0.5, maximum-scale=0.5, minimum-scale=0.5, user-scalable=no">
 <meta name="lx:category" content="firework">
 <meta name="lx:defaultAppnm" content="waimai_i">
 <meta name="lx:cid" content="c_cakhs7q">
 <meta name="lx:autopv" content="off"/>

 <link rel="preconnect" href="//s3.meituan.net" crossorigin>
 <link rel="dns-prefetch" href="//s3.meituan.net" />
 <link rel="preconnect" href="//s3plus.meituan.net" crossorigin>
 <link rel="dns-prefetch" href="//s3plus.meituan.net" />
 <link rel="preconnect" href="//lx.meituan.net" crossorigin>
 <link rel="dns-prefetch" href="//lx.meituan.net" />
 <link rel="preconnect" href="//s0.meituan.net" crossorigin>
 <link rel="dns-prefetch" href="//s0.meituan.net" />

 <link rel="dns-prefetch" href="//p0.meituan.net" />
 <link rel="dns-prefetch" href="//p1.meituan.net" />

 <link rel="preconnect" href="//marketing.waimai.meituan.com" crossorigin>
 <link rel="dns-prefetch" href="//marketing.waimai.meituan.com" />
 <link rel="preconnect" href="//promotion.waimai.meituan.com" crossorigin>
 <link rel="dns-prefetch" href="//promotion.waimai.meituan.com" />
 <link rel="preconnect" href="//3gimg.qq.com" crossorigin>
 <link rel="dns-prefetch" href="//3gimg.qq.com" />
 <link rel="dns-prefetch" href="//report.meituan.com" />
 <link rel="dns-prefetch" href="//logan.sankuai.com" />
 <link rel="dns-prefetch" href="//apis.map.qq.com" />
 <link rel="dns-prefetch" href="//postreport.meituan.com" />
 <link rel="dns-prefetch" href="//marketing-thh.meituan.com" />

 <script>
  window.globalData = {"metadata":{"owlProjectName":"gd-core-lib","env":{"isTest":false,"isStage":false,"isProd":true,"isDev":false,"isWeixin":true,"isMtApp":false,"isDpApp":false,"isNewDpApp":false,"isWmApp":false,"isQQ":false,"isWxMp":false,"isWxMpWm":false,"isWxMpMtSg":false,"isWxMpSg":false,"isWxMpMt":false,"isWxMpMtWm":false,"isWxPaoTui":false,"isTtMpMtWm":false,"isWxMpDh":false,"isWxMpPhf":false,"isMtAppPhf":false,"isWmAppPhf":false,"isZbApp":false,"isZsApp":false,"isCcbLifeApp":false,"isWxMpFls":false,"isWxHm":false,"isWxHmWm":false,"isWxHmMt":false,"isWxHmDp":false,"isWxMpYy":false},"loc":{},"serverTime":1751722414640,"bizType":0,"gdId":603954,"group":"11","preview":false,"tenant":"gundam"},"env":{"isTest":false,"isStage":false,"isProd":true,"isDev":false,"isWeixin":true,"isMtApp":false,"isDpApp":false,"isNewDpApp":false,"isWmApp":false,"isQQ":false,"isWxMp":false,"isWxMpWm":false,"isWxMpMtSg":false,"isWxMpSg":false,"isWxMpMt":false,"isWxMpMtWm":false,"isWxPaoTui":false,"isTtMpMtWm":false,"isWxMpDh":false,"isWxMpPhf":false,"isMtAppPhf":false,"isWmAppPhf":false,"isZbApp":false,"isZsApp":false,"isCcbLifeApp":false,"isWxMpFls":false,"isWxHm":false,"isWxHmWm":false,"isWxHmMt":false,"isWxHmDp":false,"isWxMpYy":false},"loc":{},"renderInfo":{"status":0,"needLocation":true,"componentRenderInfos":{"17492021128640.21859504614306646":{"render":true},"17492021128630.51393683709964160":{"render":true},"17492021128640.27880879287006390":{"render":true},"17492021128640.91274858460635800":{"render":true},"17492021128640.*****************":{"render":true},"17492021128640.95719787833924790":{"render":true},"17492021128640.92819653954569910":{"render":true},"17492021128630.55696887504935530":{"render":true},"17492021128640.70126503539635100":{"render":true},"17492021128640.02112805981291799":{"render":true},"17492021128630.50106202133299450":{"render":true},"17492021128630.16938096000619618":{"render":true},"17492021128640.64244148284375520":{"render":true},"17492021128640.27355413182951750":{"render":true},"17492021128640.89697817007450900":{"render":true},"17492021128630.24363418682416593":{"render":true},"17492021128630.84997584058451900":{"render":true},"17492021128640.60857922821113260":{"render":true},"17492021128640.79142383530808690":{"render":true},"17492021128640.51075925779569430":{"render":true},"17492021128640.39726798560790055":{"render":true},"17492021128630.41673629629826370":{"render":true},"17513760760160.11392005836033414":{"render":true},"17492021128640.37267560124306220":{"render":true},"17514664697040.14004937821477448":{"render":true},"17492021128640.03147647158617972":{"render":true},"17492021128640.59379205601244390":{"render":true},"17492021128630.08666913205156235":{"render":true},"17492021128640.29072992571039280":{"render":true},"17492021128630.63061886455951140":{"render":true},"17492021128640.15376582386052529":{"render":true},"17492021128640.76752053570265020":{"render":true},"17492021128630.92832815914661710":{"render":true},"17492021128640.55546523392291030":{"render":true},"17492021128630.68003857278868130":{"render":true},"17512724981410.3649839297285705":{"render":true},"17492021128630.07189900906650559":{"render":true},"17492021128640.64268139698045070":{"render":true},"17492021128630.59196225736726740":{"render":true},"17492021128640.54738926791874610":{"render":true},"17492021128640.25275010809517406":{"render":true},"17492021128640.90764855415729310":{"render":true},"17492021128640.66519619464941520":{"render":true},"17492021128630.94881643305277620":{"render":true},"17492021128640.07108069110338189":{"render":true},"17512722414640.7704931168051069":{"render":true},"17492021128640.16135046324489222":{"render":true},"17492021128640.85103429573472790":{"render":true},"17514268591340.6861772692322611":{"render":true},"17492021128640.94869901632953370":{"render":true},"17492021128640.92256570873661610":{"render":true},"17492021128640.50317518749118040":{"render":true},"17492021128640.22700839353252122":{"render":true},"17492021128640.65064263659236250":{"render":true},"17492021128630.71008232280866360":{"render":true},"17492021128630.29747648176339636":{"render":true},"17492021128640.64006595893889040":{"render":true},"17516404955830.15255372133206324":{"render":true},"17494544380300.12901606047316783":{"render":true},"17492021128640.47020484515403960":{"render":true},"17492021128640.71057531397986580":{"render":true},"17492021128630.35926258357579100":{"render":true},"17492021128640.30011215372788780":{"render":true},"17492021128630.67909876612110350":{"render":true},"17492021128630.32548819127511397":{"render":true},"17492021128630.50616282973028020":{"render":true},"17492021128640.21748324902812965":{"render":true},"17492021128640.58152709467555330":{"render":true},"17492021128630.17274341177100050":{"render":true},"17492021128630.48416936553053680":{"render":true},"17492021128640.23655727157819195":{"render":true},"17492021128630.49765862189392995":{"render":true},"17492021128640.32131111682148070":{"render":true},"17492021128640.25043824886131605":{"render":true},"17492021128640.18361895838916510":{"render":true},"17492021128640.84988407308714550":{"render":true},"17492021128640.05435534907275164":{"render":true},"17492021128630.87838523170184800":{"render":true},"17492021128630.00168887801072759":{"render":true},"17492021128630.82132079752245860":{"render":true},"17492021128640.74452429429458080":{"render":true},"17492021128630.43542427566244757":{"render":true},"17492021128640.64921507495553710":{"render":true},"17492021128640.19269250516509617":{"render":true},"17492021128640.31276192152288584":{"render":true},"17492021128630.56717304744144540":{"render":true},"17492021128630.94080979203214350":{"render":true},"17492021128630.02921984400600863":{"render":true},"17492021128640.11661864994773219":{"render":true},"17492021128640.19837722371615940":{"render":true},"17492021128630.27903968939090860":{"render":true},"17492021128640.98005966620431920":{"render":true},"17492021128640.62378665506973340":{"render":true},"17492021128630.02441126851207010":{"render":true},"17492021128640.01270978454390015":{"render":true}},"needGetGeneralRenderInfo":false,"gundamGeneralRenderInfo":{"validSegmentForGundamResponse":null,"firstScreenInterfacePrefetchInfo":null,"firstScreenComponent":null}},"activityId":"","gdId":603954,"gundamViewId":"2L2CWP","pageViewId":"4PmIzU","group":"11","pageInfo":{"title":"外卖红包天天领","renderStatus":0,"activityId":"","loginSwitch":false,"login":false,"extension":{"componentBundle":"[\"{\\\"name\\\":\\\"@gdc/gd-search-navbar-quanpin\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-search-navbar-quanpin\\\",\\\"bundleVersion\\\":\\\"0.21.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-search-navbar-quanpin\\\",\\\"md5\\\":\\\"3a488db3e54573acaa5119ac7da651cb\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-728-4d433536-a7c3-49d1-a741-12a503294929/mach_pro_gundam_gd-search-navbar-quanpin.zip\\\",\\\"version\\\":\\\"0.21.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-728-4d433536-a7c3-49d1-a741-12a503294929/mach_pro_gundam_gd-search-navbar-quanpin.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"100.100.100\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"100.100.100\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/wm-coupon-usepage-red-envelopes-machpro\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro\\\",\\\"bundleVersion\\\":\\\"0.8.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro\\\",\\\"md5\\\":\\\"fb1bc7abe036805195bd62a0e8e94166\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-720-d2af5b56-0da9-42cd-8c0e-2402e09fcb5d/mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro.zip\\\",\\\"version\\\":\\\"0.8.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-720-d2af5b56-0da9-42cd-8c0e-2402e09fcb5d/mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/gd-ab\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-ab\\\",\\\"bundleVersion\\\":\\\"0.11.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-ab\\\",\\\"md5\\\":\\\"066b5659a99dcbd78a4ed6a20fb97f86\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-611-d206598a-0569-42ea-802e-adb6890a8335/mach_pro_gundam_gd-ab.zip\\\",\\\"version\\\":\\\"0.11.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-611-d206598a-0569-42ea-802e-adb6890a8335/mach_pro_gundam_gd-ab.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"100.100.100\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"100.100.100\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.15\\\",\\\"h\\\":\\\"100.100.100\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/gd-goods-set\\\",\\\"createModel\\\":true,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-goods-set\\\",\\\"bundleVersion\\\":\\\"0.38.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-goods-set\\\",\\\"md5\\\":\\\"8b5e120ed0ff773c0b583a11c2943992\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-770-cf9181c1-5bd4-482f-bd18-80bc64cfc0f4/mach_pro_gundam_gd-goods-set.zip\\\",\\\"version\\\":\\\"0.38.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-770-cf9181c1-5bd4-482f-bd18-80bc64cfc0f4/mach_pro_gundam_gd-goods-set.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/wm-coupon-usepage-shop-food-machpro\\\",\\\"createModel\\\":true,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_wm-coupon-usepage-shop-food-machpro\\\",\\\"bundleVersion\\\":\\\"0.218.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_wm-coupon-usepage-shop-food-machpro\\\",\\\"md5\\\":\\\"48e6c84d0157ef267f4de019041bd7b9\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-719-6c5eaa7d-a6d6-49f0-a271-b3eba892318b/mach_pro_gundam_wm-coupon-usepage-shop-food-machpro.zip\\\",\\\"version\\\":\\\"0.218.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-719-6c5eaa7d-a6d6-49f0-a271-b3eba892318b/mach_pro_gundam_wm-coupon-usepage-shop-food-machpro.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.16\\\",\\\"h\\\":\\\"100.100.100\\\"}}}\"]","compBundleInfo":"[{\"name\":\"@gdc/gd-header-image-machpro\",\"version\":\"0.0.25\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-559-3785b56d-347e-4531-aecb-45dd1a0b28ed/bundle.js\"},{\"name\":\"@gdc/gd-ticket-wall-r\",\"version\":\"0.0.31\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-543-6fc35f88-df22-453e-b137-b63171b44aa5/bundle.js\"},{\"name\":\"@gdc/gd-goods-coupon-list\",\"version\":\"0.0.23\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-501-3bdb686d-15ec-4bd8-84ef-f9a020d04685/bundle.js\"},{\"name\":\"@gdc/gd-footer-rule-machpro\",\"version\":\"0.0.13\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-469-3b017940-9dcd-477d-b70c-83087e1ca6c2/bundle.js\"},{\"name\":\"@gdc/fx-image-react\",\"version\":\"0.0.4\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-934-d89d6621-c6aa-4c9d-a6d9-c026d6a903ec/bundle.js\"},{\"name\":\"@gundam/gundam-native-container\",\"version\":\"0.1.86\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-466-9bc23a7c-8b01-413d-9722-2dd0656fab4d/bundle.js\"}]","supportTitlebar":"true"},"scene":0,"landingRedPackage":0,"pageVersion":1751719959903,"pageVersionStr":"1751719959903"},"expiredOrOffline":false,"pageId":621317,"gd2":true,"token":"AgHeI2rVBrHQtgY4kaMM__Occt7lwJawkaPFJHqTO05k0okZzoiIaJtCMkTyTTVtbH305wA-xxqCUAAAAAACKwAARORs0OZ2ZDd6kOoXflCxN9m7v0A9JZGV-E_XtJ2YD-vImk5nI9Xrcd7wuuOJwTlG","loginSwitch":false,"login":false,"userInfo":{"token":"AgHeI2rVBrHQtgY4kaMM__Occt7lwJawkaPFJHqTO05k0okZzoiIaJtCMkTyTTVtbH305wA-xxqCUAAAAAACKwAARORs0OZ2ZDd6kOoXflCxN9m7v0A9JZGV-E_XtJ2YD-vImk5nI9Xrcd7wuuOJwTlG"},"extension":{"componentBundle":"[\"{\\\"name\\\":\\\"@gdc/gd-search-navbar-quanpin\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-search-navbar-quanpin\\\",\\\"bundleVersion\\\":\\\"0.21.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-search-navbar-quanpin\\\",\\\"md5\\\":\\\"3a488db3e54573acaa5119ac7da651cb\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-728-4d433536-a7c3-49d1-a741-12a503294929/mach_pro_gundam_gd-search-navbar-quanpin.zip\\\",\\\"version\\\":\\\"0.21.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-728-4d433536-a7c3-49d1-a741-12a503294929/mach_pro_gundam_gd-search-navbar-quanpin.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"100.100.100\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"100.100.100\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/wm-coupon-usepage-red-envelopes-machpro\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro\\\",\\\"bundleVersion\\\":\\\"0.8.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro\\\",\\\"md5\\\":\\\"fb1bc7abe036805195bd62a0e8e94166\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-720-d2af5b56-0da9-42cd-8c0e-2402e09fcb5d/mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro.zip\\\",\\\"version\\\":\\\"0.8.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-720-d2af5b56-0da9-42cd-8c0e-2402e09fcb5d/mach_pro_gundam_wm-coupon-usepage-red-envelopes-machpro.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/gd-ab\\\",\\\"createModel\\\":false,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-ab\\\",\\\"bundleVersion\\\":\\\"0.11.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-ab\\\",\\\"md5\\\":\\\"066b5659a99dcbd78a4ed6a20fb97f86\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-611-d206598a-0569-42ea-802e-adb6890a8335/mach_pro_gundam_gd-ab.zip\\\",\\\"version\\\":\\\"0.11.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-611-d206598a-0569-42ea-802e-adb6890a8335/mach_pro_gundam_gd-ab.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"100.100.100\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"100.100.100\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"100.100.100\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.15\\\",\\\"h\\\":\\\"100.100.100\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/gd-goods-set\\\",\\\"createModel\\\":true,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_gd-goods-set\\\",\\\"bundleVersion\\\":\\\"0.38.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_gd-goods-set\\\",\\\"md5\\\":\\\"8b5e120ed0ff773c0b583a11c2943992\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-770-cf9181c1-5bd4-482f-bd18-80bc64cfc0f4/mach_pro_gundam_gd-goods-set.zip\\\",\\\"version\\\":\\\"0.38.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-770-cf9181c1-5bd4-482f-bd18-80bc64cfc0f4/mach_pro_gundam_gd-goods-set.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.8.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.30\\\",\\\"h\\\":\\\"0.0.0\\\"}}}\",\"{\\\"name\\\":\\\"@gdc/wm-coupon-usepage-shop-food-machpro\\\",\\\"createModel\\\":true,\\\"machProBundleInfo\\\":{\\\"bundleName\\\":\\\"mach_pro_gundam_wm-coupon-usepage-shop-food-machpro\\\",\\\"bundleVersion\\\":\\\"0.218.0\\\",\\\"mach_id\\\":\\\"mach_pro_gundam_wm-coupon-usepage-shop-food-machpro\\\",\\\"md5\\\":\\\"48e6c84d0157ef267f4de019041bd7b9\\\",\\\"tags\\\":\\\"waimai-homepage\\\",\\\"url\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-719-6c5eaa7d-a6d6-49f0-a271-b3eba892318b/mach_pro_gundam_wm-coupon-usepage-shop-food-machpro.zip\\\",\\\"version\\\":\\\"0.218.0\\\",\\\"dioUrl\\\":\\\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-719-6c5eaa7d-a6d6-49f0-a271-b3eba892318b/mach_pro_gundam_wm-coupon-usepage-shop-food-machpro.dio.zip\\\"},\\\"dependencies\\\":{\\\"app\\\":{\\\"android\\\":{\\\"wm\\\":{\\\"l\\\":\\\"8.1.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}},\\\"ios\\\":{\\\"wm\\\":{\\\"l\\\":\\\"7.102.0\\\",\\\"h\\\":\\\"0.0.0\\\"},\\\"mt\\\":{\\\"l\\\":\\\"12.9.200\\\",\\\"h\\\":\\\"0.0.0\\\"}}},\\\"mainBundle\\\":{\\\"l\\\":\\\"0.0.16\\\",\\\"h\\\":\\\"100.100.100\\\"}}}\"]","compBundleInfo":"[{\"name\":\"@gdc/gd-header-image-machpro\",\"version\":\"0.0.25\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-559-3785b56d-347e-4531-aecb-45dd1a0b28ed/bundle.js\"},{\"name\":\"@gdc/gd-ticket-wall-r\",\"version\":\"0.0.31\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-543-6fc35f88-df22-453e-b137-b63171b44aa5/bundle.js\"},{\"name\":\"@gdc/gd-goods-coupon-list\",\"version\":\"0.0.23\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-501-3bdb686d-15ec-4bd8-84ef-f9a020d04685/bundle.js\"},{\"name\":\"@gdc/gd-footer-rule-machpro\",\"version\":\"0.0.13\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-469-3b017940-9dcd-477d-b70c-83087e1ca6c2/bundle.js\"},{\"name\":\"@gdc/fx-image-react\",\"version\":\"0.0.4\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-934-d89d6621-c6aa-4c9d-a6d9-c026d6a903ec/bundle.js\"},{\"name\":\"@gundam/gundam-native-container\",\"version\":\"0.1.86\",\"url\":\"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-466-9bc23a7c-8b01-413d-9722-2dd0656fab4d/bundle.js\"}]","supportTitlebar":"true"},"config":{"wyxRedPackageImgUrl":"https://p0.meituan.net/scarlett/a5003af03006afcecc76e1338bc12f51193252.gif","owl":{"maxErrCount":5,"maxErrTime":1,"limitSwitch":true},"uds":{"isHitUds":true,"isAllComponent":false,"componentList":["@gdc/single-line-shoplist","@gdc/sg-single-line-shoplist","@gdc/gd-single-line-shoplist"],"rate":100},"reactLazyLoadConfig":{"lazyLoadSwitch":true,"firstScreenNum":4,"noLazyPages":[567999,575234,560409,582015,582598,582417,582177,583141,583127,583111,583009,582890,582334,591515,591882,593092,610237,605432,607577,614980,611529,614440,615702,615616,614979,614978],"lazyPages":[],"noLazyComponents":["@gdc/wm-marketing-onemeal-title-new","@gdc/gd-top-navigation-machpro","@gdc/gd-bailout-machpro","@gdc/gd-location-bar-machpro","@gdc/gd-bottom-navigation","@gdc/gd-float-machpro","@gdc/gd-float-navigation","@gdc/gd-fall-ticket-r"],"onlyRunInJSThread":false},"jumpSegmentIdsCheckWhiteList":[596328,560697,560699],"segmentUseGundamDomainList":[592597,560697,560699],"lazyLoadConfig":{"noLazyComponents":["gdc-wm-bailout","gdc-wm-ticket-descend","gdc-wyx-bailout-position","gdc-wyx-bailout-provide","gdc-wyx-single-line-shoplist-precise","gdc-wm-bottom-navigation","gdc-thh-bottom-navigation","gdc-wm-side-rule","gdc-thh-slide-float-guide","gdc-wm-float","gdc-wyx-float-red-package","gdc-wyx-red-package","gdc-wm-callapp","gdc-x18-coupon-rain-game","gdc-wm-treasure-chest","gdc-wm-god-ticket-descend","gdc-wyx-float-red-package","gdc-wm-search","gdc-wm-location-bar","gdc-wm-sq-tasks-perception","gdc-wm-sq-tasks-perception-young","gdc-thh-shopping-cart","gdc-sqt-official-bottom","gdc-sqt-official-phone","gdc-wm-tab-anchor","gdc-wm-welfare-center-activity-modal","gdc-gd-shopping-cart","gdc-wm-marketing-ic-giftpurchase","gdc-wm-welfare-bottom-navigation","gdc-fx-private-traffic-welfare-bottom-navigation"],"lazyLoadSwitch":true,"firstScreenNum":4,"scrollLoadNum":1,"lazyLoadGraySwitch":false,"lazyLoadGrayRatio":100,"grayPageIds":[],"urlMatchRule":"/o/activity/segment|/gundam/common/query","blackComponents":[],"grayComponents":[],"grayCompSwitch":false,"noSubPackageComponents":["@gdc/wm-tab-plus"],"blackPageIds":["20534","445582","492601","486500","500704","500370","504852","505239","503061","427752","539166","537894","538095","537807","537809","542747","539166","539153","542770","556664","550749","477934","562362","561810","544602","580051","579379","64165","586628","586635","588598","588014","587901","588598","587393","544602","589184","589237","585800","599596","612813","611840"],"grayGroupIds":["2","44"]},"supplyTokenConfig":{"key":"APJQ3LXTSLK720IS","iv":"W28U5S4R2X1MDX4K"},"couponSkip":{"default":{"default":true},"dimType":1},"hydratePageIds":[586300,592434],"partialRenderConfig":{"enable":true,"disablePageIds":[],"disableGroupIds":[]},"locationConfig":{"cache":true,"cacheTime":180000},"sgBizGroupList":[2,59,60,71],"signConfig":{"switch":true,"whiteList":["/o/activity/segment","/o/activity/segmentData","/o/activity/segmentGroup","/o/activity/segmentDataByUDS","/o/activity/segmentGroupByUDS"]},"fixBlackLine":{"group":[{"groupId":"87","containerEnv":0}],"activity":[{"gdId":"","containerEnv":0}],"page":[{"pageId":"477934","containerEnv":1},{"pageId":"576180","containerEnv":1},{"pageId":"576193","containerEnv":1},{"pageId":"560697","containerEnv":1},{"pageId":"560699","containerEnv":1},{"pageId":"579950","containerEnv":1},{"pageId":"579356","containerEnv":1},{"pageId":"581157","containerEnv":1},{"pageId":"581186","containerEnv":1},{"pageId":"580901","containerEnv":1},{"pageId":"570161","containerEnv":1},{"pageId":"569714","containerEnv":1},{"pageId":"570097","containerEnv":1},{"pageId":"570099","containerEnv":1},{"pageId":"570102","containerEnv":1},{"pageId":"570104","containerEnv":1},{"pageId":"570110","containerEnv":1},{"pageId":"570111","containerEnv":1},{"pageId":"570112","containerEnv":1},{"pageId":"570114","containerEnv":1},{"pageId":"570116","containerEnv":1},{"pageId":"566196","containerEnv":1},{"pageId":"566197","containerEnv":1},{"pageId":"570117","containerEnv":1},{"pageId":"570118","containerEnv":1},{"pageId":"570119","containerEnv":1},{"pageId":"570120","containerEnv":1},{"pageId":"569908","containerEnv":1},{"pageId":"569910","containerEnv":1},{"pageId":"570148","containerEnv":1},{"pageId":"581637","containerEnv":1},{"pageId":"581631","containerEnv":1},{"pageId":"581811","containerEnv":1},{"pageId":"581638","containerEnv":1},{"pageId":"581633","containerEnv":1},{"pageId":"581813","containerEnv":1},{"pageId":"581634","containerEnv":1},{"pageId":"581635","containerEnv":1},{"pageId":"581636","containerEnv":1}]},"preKNBWhiteList":[476800],"jsonpRetryTimes":3,"removeDomainConfig":[{"switch":true,"host":"marketing.waimai.meituan.com","paths":["/o/activity","/o/poi-coupon","/o/strategy","/o/outeractivity"]},{"switch":true,"host":"promotion.waimai.meituan.com","paths":["/lottery/limitcouponcomponent/getTime","/playcenter/generalcoupon/info","/playcenter/common/v1/login","/playcenter/common/v1/mycoupons/shenquan","/playcenter/intelligentmeeting/info","/playcenter/common/v1/shenquan/amount/max","/playcenter/signlottery/info","/playcenter/common/v1/coupon/shenquan/used","/playcenter/common/v1/entry","/playcenter/couponFestivalAsset/myAssets","/playcenter/couponFestivalTab/tabStatus","/lottery/couponcomponent/info/v2","/lottery/rights/couponwallcomponent/info","/taskPlatform/c/share/preSendCoupon"]}],"fallbackConfig":{"pagesId":[]},"wyxRedPackageUrl":"https://market.waimai.meituan.com/gd/single.html?el_biz=waimai&el_page=gundam.loader&gundam_id=1shSoN&activity_id=135574&utm_source=70200","oldCouponCache":false,"callAppFlag":true,"segmentNeedToken":true,"axiosRetryTimes":3,"newCouponDetail":true,"performance":{"switch":true,"whitePageIdList":[586300,582991,582988]},"useTreeShaking":false,"locationBar":{"name":"@gdc/gd-location-bar-machpro","version":"0.0.30","url":"https://s3plus.meituan.net/v1/mss_6ef9607fc4d44ceb8fe3dd19a7c98809/gundam-component/component-470-b30ff5e4-b150-4900-a789-c828e6998c04/bundle.js"},"jumpOldCouponLink":false,"dynamicImg":true,"limitedSaleWithRefreshConfig":{"limitedSaleWithRefreshDataList":["615792","614071","618999","619002"],"delayTime":1500},"gundamProConfig":{"maxHideCompNum":1},"riskConfig":{"switch":true,"ignoreList":["/api/v1/store/location/recall","/playcenter/common/v1/receivinginfo","/playcenter/common/v1/getreceivinginfo/v2","/playcenter/eurocup/guess","/playcenter/common/v1/achievement/completed/reward/get","/lottery/couponpkg/promotioninfo","/lottery/vip/promotioninfo","/lottery/vip/popup","/lottery/slotmachine/picture","/o/strategy/topicId","/o/strategy/headPicInfo","/o/dsp/getGdTopInfo","m.dianping.com/getlocalcityid","adapi.waimai.meituan.com/api/flashbuy/activity","thh.meituan.com/api/mtmall/search/hotKeyWord","marketing-thh.meituan.com/api/activity/coupon/getUserMagicCoupon","marketing-thh.meituan.com/api/activity/coupon/expand","marketing-thh.meituan.com/api/marketing/gundam/queryLike","marketing-thh.meituan.com/api/marketing/gundam/common/query","marketing-thh.meituan.com/api/activity/taskGroup/reward","marketing-thh.meituan.com/api/activity/coupon/expand","marketing-thh.meituan.com/api/activity/coupon/popCoupon","/activity/task/shopcoupon/entry","/activity/task/shopcoupon/award/action","/shareaward/getaward","/i/vp/member/gundam/open_member","/i/vp/member/reserve_activity","/i/vp/member/gundam/member_day_component","/vp/v4/common_submit","/vp/coupon/cancel","promote.waimai.meituan.com/weeklyCard/toBuyWeeklyCard","promote.waimai.meituan.com/weeklyCard/toBuyWeeklyCardV2","activity.waimai.meituan.com/activity/team","offsiteact.meituan.com/act/uas/get_list","b-sqt.meituan.com/api/leads","apimeishi.meituan.com/activity/alcedo/magicDeal","apimeishi.meituan.com/api/dc/investment/getMaterialList"]},"treeShakingConfig":{"grayPageIds":[],"useTreeShaking":true,"blackPageIds":["311035","319688"],"grayGroupIds":[]}},"userTypeInfo":"","pageConfigJsonUrl":"","gdBs":"0000","testUsageType":false};
    window.renderInfo = {"status":0,"needLocation":true,"componentRenderInfos":{"17492021128640.21859504614306646":{"render":true},"17492021128630.51393683709964160":{"render":true},"17492021128640.27880879287006390":{"render":true},"17492021128640.91274858460635800":{"render":true},"17492021128640.*****************":{"render":true},"17492021128640.95719787833924790":{"render":true},"17492021128640.92819653954569910":{"render":true},"17492021128630.55696887504935530":{"render":true},"17492021128640.70126503539635100":{"render":true},"17492021128640.02112805981291799":{"render":true},"17492021128630.50106202133299450":{"render":true},"17492021128630.16938096000619618":{"render":true},"17492021128640.64244148284375520":{"render":true},"17492021128640.27355413182951750":{"render":true},"17492021128640.89697817007450900":{"render":true},"17492021128630.24363418682416593":{"render":true},"17492021128630.84997584058451900":{"render":true},"17492021128640.60857922821113260":{"render":true},"17492021128640.79142383530808690":{"render":true},"17492021128640.51075925779569430":{"render":true},"17492021128640.39726798560790055":{"render":true},"17492021128630.41673629629826370":{"render":true},"17513760760160.11392005836033414":{"render":true},"17492021128640.37267560124306220":{"render":true},"17514664697040.14004937821477448":{"render":true},"17492021128640.03147647158617972":{"render":true},"17492021128640.59379205601244390":{"render":true},"17492021128630.08666913205156235":{"render":true},"17492021128640.29072992571039280":{"render":true},"17492021128630.63061886455951140":{"render":true},"17492021128640.15376582386052529":{"render":true},"17492021128640.76752053570265020":{"render":true},"17492021128630.92832815914661710":{"render":true},"17492021128640.55546523392291030":{"render":true},"17492021128630.68003857278868130":{"render":true},"17512724981410.3649839297285705":{"render":true},"17492021128630.07189900906650559":{"render":true},"17492021128640.64268139698045070":{"render":true},"17492021128630.59196225736726740":{"render":true},"17492021128640.54738926791874610":{"render":true},"17492021128640.25275010809517406":{"render":true},"17492021128640.90764855415729310":{"render":true},"17492021128640.66519619464941520":{"render":true},"17492021128630.94881643305277620":{"render":true},"17492021128640.07108069110338189":{"render":true},"17512722414640.7704931168051069":{"render":true},"17492021128640.16135046324489222":{"render":true},"17492021128640.85103429573472790":{"render":true},"17514268591340.6861772692322611":{"render":true},"17492021128640.94869901632953370":{"render":true},"17492021128640.92256570873661610":{"render":true},"17492021128640.50317518749118040":{"render":true},"17492021128640.22700839353252122":{"render":true},"17492021128640.65064263659236250":{"render":true},"17492021128630.71008232280866360":{"render":true},"17492021128630.29747648176339636":{"render":true},"17492021128640.64006595893889040":{"render":true},"17516404955830.15255372133206324":{"render":true},"17494544380300.12901606047316783":{"render":true},"17492021128640.47020484515403960":{"render":true},"17492021128640.71057531397986580":{"render":true},"17492021128630.35926258357579100":{"render":true},"17492021128640.30011215372788780":{"render":true},"17492021128630.67909876612110350":{"render":true},"17492021128630.32548819127511397":{"render":true},"17492021128630.50616282973028020":{"render":true},"17492021128640.21748324902812965":{"render":true},"17492021128640.58152709467555330":{"render":true},"17492021128630.17274341177100050":{"render":true},"17492021128630.48416936553053680":{"render":true},"17492021128640.23655727157819195":{"render":true},"17492021128630.49765862189392995":{"render":true},"17492021128640.32131111682148070":{"render":true},"17492021128640.25043824886131605":{"render":true},"17492021128640.18361895838916510":{"render":true},"17492021128640.84988407308714550":{"render":true},"17492021128640.05435534907275164":{"render":true},"17492021128630.87838523170184800":{"render":true},"17492021128630.00168887801072759":{"render":true},"17492021128630.82132079752245860":{"render":true},"17492021128640.74452429429458080":{"render":true},"17492021128630.43542427566244757":{"render":true},"17492021128640.64921507495553710":{"render":true},"17492021128640.19269250516509617":{"render":true},"17492021128640.31276192152288584":{"render":true},"17492021128630.56717304744144540":{"render":true},"17492021128630.94080979203214350":{"render":true},"17492021128630.02921984400600863":{"render":true},"17492021128640.11661864994773219":{"render":true},"17492021128640.19837722371615940":{"render":true},"17492021128630.27903968939090860":{"render":true},"17492021128640.98005966620431920":{"render":true},"17492021128640.62378665506973340":{"render":true},"17492021128630.02441126851207010":{"render":true},"17492021128640.01270978454390015":{"render":true}},"needGetGeneralRenderInfo":false,"gundamGeneralRenderInfo":{"validSegmentForGundamResponse":null,"firstScreenInterfacePrefetchInfo":null,"firstScreenComponent":null}};
   window.gdTplVersion = "ftl-0.4.86";

  // 兼容NodeList
  if (window.NodeList && !NodeList.prototype.forEach) {
    NodeList.prototype.forEach = Array.prototype.forEach;
  }
 </script>
 <script>
  var searchParams = location.search.replace('?','').split('&').filter(function(item){ return !!item }).reduce(function(pre,item){ var arr = item.split('='); if(arr.length=== 2){pre[arr[0]] = arr[1]} return pre},{});
 </script>

 <script>
   /**
    * owl错误模块
    */
    "use strict";!function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"_Owl_",a=window;a[e]||(a[e]={isRunning:!1,isReady:!1,preTasks:[],dataSet:[],pageData:[],disableMutaObserver:!1,observer:null,use:function(e,t){this.isReady&&a.Owl&&a.Owl[e](t),this.preTasks.push({api:e,data:[t]})},add:function(e){this.dataSet.push(e)},run:function(){var t=this;if(!this.isRunning){this.isRunning=!0;var e=a.onerror;a.onerror=function(){this.isReady||this.add({type:"jsError",data:arguments}),e&&e.apply(a,arguments)}.bind(this),(a.addEventListener||a.attachEvent)("error",function(e){t.isReady||t.add({type:"resError",data:[e]})},!0);var i=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,r=window.performance||window.WebKitPerformance;if(i&&r){var n=-1,s=window.navigator.userAgent;if(-1<s.indexOf("compatible")&&-1<s.indexOf("MSIE")?(new RegExp("MSIE (\\d+\\.\\d+);").test(s),n=parseFloat(RegExp.$1)):-1<s.indexOf("Trident")&&-1<s.indexOf("rv:11.0")&&(n=11),-1!==n&&n<=11)return void(this.disableMutaObserver=!0);try{this.observer=new i(function(e){t.pageData.push({mutations:e,startTime:r.now()})}),this.observer.observe(document,{childList:!0,subtree:!0})}catch(e){console.log("mutationObserver err")}}else this.disableMutaObserver=!0}}},a[e].run())}();
   /**
    * lx
    */
   !(function (win, doc, ns) {
     var cacheFunName = '_MeiTuanALogObject';
     win[cacheFunName] = ns;
     if (!win[ns]) {
       var _LX = function () {
         _LX.q.push(arguments);
         return _LX;
       };
       _LX.q = _LX.q || [];
       _LX.l = +new Date();
       win[ns] = _LX;
     }
   })(window, document, 'LXAnalytics');

   window._KNB_WX_LOADER = true;
 </script>

 <script>
  /**
   * perf 预加载模块
   */
  !function(){"use strict";!function(e,r){if(e.perf=e.perf||function(){try{var r=Array.prototype.slice.apply(arguments);"init"===r[0]&&r[1]&&"object"==typeof r[1]&&(e._perf_.initOptions=r[1])}catch(n){}},e._perf_=e._perf_||{js_error_queue:[],unhandle_rejection_queue:[],mutation_queue:[],res_error_queue:[]},!e._perf_.isReady){var n=window.onerror;window.onerror=function(){e._perf_.isReady||e._perf_.js_error_queue.push(arguments),null==n||n.apply(window,arguments)},window.addEventListener("unhandledrejection",(function(r){e._perf_.isReady||e._perf_.unhandle_rejection_queue.push(r)})),window.addEventListener("error",(function(r){e._perf_.isReady||e._perf_.res_error_queue.push(r)}),!0);var o=e.performance;if((null==o?void 0:o.now)&&e.MutationObserver)try{e._perf_.observer=new e.MutationObserver((function(r){e._perf_.mutation_queue.push({mutations:r,startTime:Date.now()})})),e._perf_.observer.observe(r,{childList:!0,subtree:!0})}catch(t){}}}(window,document)}();
 </script>

<script>
  // 根据灰度条件调整font-size和meta标签
  // 修改META信息
  var modifyMeta = function(meta, metaEl) {
    metaEl.setAttribute('content', meta.content);
  };
  // 新增META信息
  var addMeta = function(meta) {
    // head元素
    var head = document.querySelector('head');
    var metaEl = document.createElement('meta');
    metaEl.setAttribute('name', meta.name);
    metaEl.setAttribute('content', meta.content);
    head.appendChild(metaEl)
  };
  // 修改或新增META信息
  var modifyOrAddMeta = function(metaList) {
    metaList.map(function(meta) {
      var hasMeta = false;
      if (meta) {
        var metaEls = document.querySelectorAll('meta[name="' + meta.name + '"]');
        if (metaEls && metaEls.length) {
          var metaEl = metaEls[metaEls.length - 1];
          if (metaEl) {
            hasMeta = true;
            modifyMeta(meta, metaEl);
          }
        }
      }

      if (!hasMeta) {
        addMeta(meta);
      }
    });
  };
  // 判断是否是app环境
  var isApp = function(globalDataEnv) {
    if (globalDataEnv.isMtApp ||
      globalDataEnv.isDpApp ||
      globalDataEnv.isNewDpApp ||
      globalDataEnv.isWmApp) 
    {
      return true;
    }
    return false;
  }
  // 判断是否命中去除黑线灰度条件
  var isDeletBlackLine = function(configList, id, idName) {
    // 不存在条件就返回false
    if (!configList) return false;
    var isInApp = isApp(window.globalData.env);
    for (var i = 0; i < configList.length; i++) {
      var config = configList[i];
      if (config && config[idName] === id) {
        // 未定义或者等于0表示直接全量
        if (config.containerEnv === undefined || config.containerEnv === 0) {
          return true;
        } else if(config.containerEnv === 1) { // 1表示在美团、外卖、点评app环境
            return isInApp;
        } else if(config.containerEnv === 2) { // 2表示除上述三个app外其他环境
            return !isInApp;
        }
      }
    }
    return false;
  };
  var fixBlackLineConfig = window.globalData.config && window.globalData.config.fixBlackLine || {};
  var fixBlackLinePageList = fixBlackLineConfig.page || [];
  var fixBlackLineGdList = fixBlackLineConfig.activity || [];
  var fixBlackLineGroupList = fixBlackLineConfig.group || []; 
  var gdPageId = window.globalData.pageId + '';
  var gdId = window.globalData.gdId + '';
  var groupId = window.globalData.group + '';
  // 是否保留两位小数标记
  var isToFixed = false;
  // 三个条件满足一个即可
  if (isDeletBlackLine(fixBlackLinePageList, gdPageId, 'pageId') || isDeletBlackLine(fixBlackLineGdList, gdId, 'gdId') || isDeletBlackLine(fixBlackLineGroupList, groupId, 'groupId')) {
    modifyOrAddMeta([
    {
      name: 'viewport',
      content: 'width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1,viewport-fit=cover',
    },
    ]);
    // 标记命中灰度条件，font-size保留两位小数
    isToFixed = true;
  }
  (function flexible(window, document, isToFixed) {
    var docEl = document.documentElement
    //  375屏幕的font-size是100，也就是说，iphone6的font-size是100，设计稿按照750px设计，0.01rem = 1px
    function setRemUnit() {
      var rem = docEl.clientWidth / 7.5
      if (isToFixed) {
        docEl.style.fontSize = rem.toFixed(2) + 'px'
      } else {
          docEl.style.fontSize = Math.floor(rem) + 'px'
      }
    }
    setRemUnit()
    // reset rem unit on page resize
    window.addEventListener('resize', setRemUnit)
  }(window, document, isToFixed))
 </script>

 <script crossorigin="anonymous" type="text/javascript" src="//lx.meituan.net/lx.js" charset="utf-8" async></script>

 <script>
   var l = window.location;
   var lh = l.href;
   if(lh && lh.indexOf('wm_uuid') > -1 && (lh.indexOf("ctype=wm_wxapp") > -1 || lh.indexOf("ctype=sg_wxapp") > -1)){
      var match = lh.match(/wm_uuid=([\-a-zA-Z0-9]*)/);
      if(match.length >= 2) {
        LXAnalytics('set', 'uuid', match[1])
      }
   }

   // 通过ctype设置lx appnm
   if((searchParams && searchParams.ctype) || window.navigator.userAgent.toLowerCase().indexOf('wxde8ac0a21135c07d') !== -1) {
    var ctypeAppnmMap = {
      dh_weapp: 'waima_wxapp',   // 歪马小程序
      wm_wxapp: 'waimai_wxapp',  // 外卖小程序
      mt_mp: 'group_wxapp',      // 美团小程序, 美团小程序外卖子包
      thh_mt_wxapp: 'group_wxapp',   // 美团小程序团好货子包
      mt_weapp: 'group_wxapp',   // 美团小程序闪购子包
      sg_wxapp: 'shangou',        // 闪购小程序
      thh_wxapp: 'tuanhaohuo_wxapp',  // 团好货小程序
      fulishe_wxapp: 'siyufuli_wxapp',  // 私域福利社小程序
      paotui: 'paotui_c_miniprograms' // 跑腿小程序
    };

    var currentAppnm = window.navigator.userAgent.toLowerCase().indexOf('wxde8ac0a21135c07d') !== -1 ? 'group_wxapp' : ctypeAppnmMap[searchParams.ctype];
    if(currentAppnm) {
      LXAnalytics('set', 'appnm', currentAppnm);
    }
  }
 </script>

<style type="text/css">
@font-face{font-family:"Avenir Medium";src:url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirm.woff2") format("woff2"),url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirm.woff") format("woff"),url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirm.ttf") format("truetype")}@font-face{font-family:"Avenir Book";src:url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirb.woff2") format("woff2"),url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirb.woff") format("woff"),url("//s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/avenirb.ttf") format("truetype")}
</style>

</head>

<body>

<style>.hydra-loading{position:fixed;top:0;left:0;width:100%;height:100%;z-index:9999}.hydra-loading .hydra-loading-content{position:absolute;top:50%;left:50%;width:1rem;height:1rem;margin-top:-.5rem;margin-left:-.5rem;border-radius:.1rem;background:rgba(0,0,0,0.2)}.hydra-preloader{display:block;margin:.2rem auto;width:.6rem;height:.6rem;-webkit-animation:hydra-spin 1s steps(12,end) infinite;animation:hydra-spin 1s steps(12,end) infinite}@-webkit-keyframes hydra-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes hydra-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}</style>
<div id="J_hydraLoader" class="hydra-loading">
<div class="hydra-loading-content">
    <img class="hydra-preloader" src="data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cline id='l' x1='60' x2='60' y1='7' y2='27' stroke='%23fff' stroke-width='11' stroke-linecap='round'/%3E%3C/defs%3E%3Cg%3E%3Cuse xlink:href='%23l' opacity='.27'/%3E%3Cuse xlink:href='%23l' opacity='.27' transform='rotate(30 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.27' transform='rotate(60 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.27' transform='rotate(90 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.27' transform='rotate(120 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.27' transform='rotate(150 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.37' transform='rotate(180 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.46' transform='rotate(210 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.56' transform='rotate(240 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.66' transform='rotate(270 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.75' transform='rotate(300 60,60)'/%3E%3Cuse xlink:href='%23l' opacity='.85' transform='rotate(330 60,60)'/%3E%3C/g%3E%3C/svg%3E"/>
</div>
</div>
<script>
try {
  window.addEventListener('load', function(){ document.getElementById('J_hydraLoader').style.display = 'none'});setTimeout(function(){document.getElementById('J_hydraLoader').style.display = 'none';}, 3000);
} catch (e) {
  console.log(e);
}
</script>

<!-- perf 2.0 秒开率模块 -->
<script src='https://s3.meituan.net/mnpm-cdn/@mtfe-perf-h5-1.8.0/web.fsp.min.js'></script>

<script>
if (!searchParams.wm_opr) {
  window.perf('init', {
    project: window.globalData.env.isProd ? 'gundam' : 'gundam-test', // 在配置中心中 申请到的关联 metrics 服务后别名
    pagePath: 'GDID_' + window.globalData.gdId,
    fsp2: {
      disable: false,
      sampleRate: window.globalData.env.isProd ? 50 : 100,
      debug: searchParams && searchParams.perfDebug === 'true',
      customTags: {
        group: window.globalData.group,
      },
    },
    // 接入秒开 2.0 后记得将原有的旧 FSP 指标置为 disable 避免引起过多的性能损耗！！！！
    fsp: {
      disable: true,
    },
  });
  // 站外场景临时通过手动 set 的方式设置 tag
  window.perf('setTag', { group: window.globalData.group });
}
</script>


<div id="app">
    <div id="prerender-data"><script>window.noPreLoc=!1</script><script>var bodyEle=document.querySelector("body"),oldStyle=bodyEle.getAttribute("style"),newStyle=oldStyle?oldStyle+";background-color: rgb(245, 245, 245);":"background-color: rgb(245, 245, 245);";bodyEle.setAttribute("style",newStyle)</script><style>.netunion-red-envelope .new-wangmeng-wrap[data-v-dad6c396] { position: relative; width: 100%; background-size: 100% 100%; background-repeat: no-repeat; } .netunion-red-envelope .red-ellipsis[data-v-dad6c396] { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; } .netunion-red-envelope .red-bag[data-v-dad6c396] { width: 100%; padding: 0px 0.2rem; box-sizing: border-box; } .netunion-red-envelope .red-bag .top-title[data-v-dad6c396] { width: 5.2rem; height: 1.04rem; line-height: 0.8rem; font-size: 0.32rem; color: rgb(27, 28, 29); margin-bottom: 0.04rem; padding-top: 0.22rem; padding-left: 0.26rem; box-sizing: border-box; font-family: "PingFangSC-Semibold, Microsoft YaHei Arial"; } .netunion-red-envelope .red-bag .top-title .x[data-v-dad6c396] { font-size: 0.48rem; color: rgb(255, 37, 13); margin: 0px 0.04rem; font-family: Arial; font-weight: 700; } .netunion-red-envelope .red-bag .top-title .txt[data-v-dad6c396] { line-height: 0.7rem; } .netunion-red-envelope .red-bag .red-before[data-v-dad6c396] { width: 100%; height: 3.25rem; box-sizing: border-box; padding-bottom: 0.2rem; background-size: 100% 2.94rem; background-position: center top; background-repeat: no-repeat; position: relative; } .netunion-red-envelope .red-bag .red-before .fake-red-bags[data-v-dad6c396] { display: flex; justify-content: space-between; padding: 0px 0.2rem; box-sizing: border-box; } .netunion-red-envelope .red-bag .red-before .fake-red-bags .fake-single[data-v-dad6c396] { width: 2.14rem; height: 1.62rem; background-repeat: no-repeat; background-position: center center; background-size: 100% 100%; padding-top: 0.08rem; box-sizing: border-box; } .netunion-red-envelope .red-bag .red-before .fake-red-bags .fake-single .fake-name[data-v-dad6c396] { font-weight: 400; font-family: PingFangSC-Regular; font-size: 0.2rem; color: rgb(137, 138, 139); text-align: center; } .netunion-red-envelope .red-bag .red-before .bottom-position-bg-img[data-v-dad6c396] { width: 100%; height: 2.02rem; position: absolute; bottom: 0px; left: 0px; background-repeat: no-repeat; background-position: center center; background-size: 100% 100%; } .netunion-red-envelope .red-bag .red-before .receive-btn[data-v-dad6c396] { width: 2.54rem; height: 0.6rem; position: absolute; left: 50%; transform: translateX(-50%); bottom: 0.54rem; line-height: 0.6rem; text-align: center; background-image: linear-gradient(0deg, rgb(255, 217, 120) 14%, rgb(255, 255, 255) 83%); border-radius: 0.44rem; font-weight: 600; font-family: PingFangSC-Semibold; font-size: 0.32rem; color: rgb(255, 0, 0); } .netunion-red-envelope .red-bag .red-before .btn-tip-text[data-v-dad6c396] { font-weight: 400; opacity: 0.85; font-family: PingFangSC-Regular; font-size: 0.22rem; color: rgb(255, 255, 255); text-align: center; position: absolute; left: 50%; transform: translateX(-50%); bottom: 0.18rem; } .text-overflow, .gdc-newest-ticket-wall .ticket-line .ticket-item .right .p1, .gdc-newest-ticket-wall .ticket-line .ticket-item .right .p2, .gdc-newest-ticket-wall .ticket-line .ticket-item .right .p3, .gdc-newest-ticket-wall .ticket-line .new-ticket-item .right .p1, .gdc-newest-ticket-wall .ticket-line .new-ticket-item .right .p2, .gdc-newest-ticket-wall .ticket-line.one-line-3 .new-ticket-item .left .p3 { white-space: nowrap; overflow: hidden; word-break: break-all; text-overflow: ellipsis; } .gdc-newest-ticket-wall .ticket-line { padding: 0px 0.24rem; } .gdc-newest-ticket-wall .ticket-line:first-child { margin-top: 0px; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item { display: inline-flex; width: 100%; box-sizing: border-box; overflow: hidden; height: 1.6rem; position: relative; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .left { flex-basis: 1.78rem; flex-shrink: 0; display: flex; flex-direction: column; justify-content: center; align-items: center; font-weight: 400; font-family: AvenirLTPro-Heavy; font-size: 0.24rem; color: rgb(255, 74, 38); text-align: center; padding-top: 0px; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .left .p1 { font-size: 0.6rem; text-align: left; display: flex; align-items: flex-end; letter-spacing: 0px; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .left .p1 .unit { font-size: 0.28rem; text-align: left; line-height: 0.21rem; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .left .p1 .num { line-height: 0.56rem; height: 0.45rem; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .left .p2 { font-weight: 400; font-family: PingFangSC-Regular; font-size: 0.24rem; color: rgb(255, 74, 38); letter-spacing: 0px; text-align: left; line-height: 0.24rem; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .right { flex: 1 1 0%; font-weight: 500; font-family: PingFangSC-Medium; font-size: 0.32rem; color: rgb(34, 36, 38); letter-spacing: 0px; text-align: left; line-height: 0.32rem; display: flex; flex-direction: column; justify-content: center; padding: 0px; } .gdc-newest-ticket-wall .ticket-line .new-ticket-item .right .p1 { max-width: 3rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item { width: 3.47rem; height: 1.86rem; margin-left: 0.08rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item:nth-child(2n+1) { margin-left: 0px; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item .left { flex-basis: 1.51rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item .left .p1 { font-size: 0.54rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item .left .p2 { margin-top: 0.16rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item .right { justify-content: flex-start; font-size: 0.26rem; line-height: 0.28rem; padding-top: 0.56rem; } .gdc-newest-ticket-wall .ticket-line.one-line-2 .new-ticket-item .right .p1 { max-width: 1.86rem; } .no-coupon-container[data-v-0d59191c] { width: 6.54rem; height: 2rem; margin: 0.16rem 0.24rem 0px; background: rgb(255, 255, 255); border-radius: 0.16rem; display: flex; align-items: center; justify-content: center; gap: 0.06rem; } .no-coupon-container .no-coupon-pic[data-v-0d59191c] { display: block; width: 2.21rem; height: 1.66rem; border: 0px; } .no-coupon-container .no-coupon-tips[data-v-0d59191c] { width: 2.25rem; font-weight: 400; font-family: PingFangSC-Regular; font-size: 0.22rem; color: rgb(87, 88, 89); letter-spacing: 0px; text-align: center; line-height: 0.32rem; } .no-coupon-container.short-line[data-v-0d59191c] { height: 1.6rem; } .no-coupon-container.short-line .no-coupon-pic[data-v-0d59191c] { width: 2rem; height: 1.3rem; } .killsecond-tab[data-v-3fb825e3] { border-top-left-radius: 0.24rem; border-top-right-radius: 0.24rem; } .killsecond-tab .tab-list-multi .scroll-list[data-v-3fb825e3] { display: flex; height: 1.06rem; padding-top: 0.2rem; box-sizing: border-box; padding-left: 0.24rem; } .killsecond-tab .tab-list-multi .scroll-list .tab-item[data-v-3fb825e3] { display: flex; flex-direction: column; align-items: center; width: 0.8rem; height: 0.82rem; border-radius: 0.12rem; padding-top: 0.12rem; line-height: 1; box-sizing: border-box; flex-shrink: 0; } .killsecond-tab .tab-list-multi .scroll-list .tab-item .time[data-v-3fb825e3] { font-family: PingFangSC-Medium; font-size: 0.32rem; text-align: center; line-height: 0.28rem; } .killsecond-tab .tab-list-multi .scroll-list .tab-item .time .time-hms[data-v-3fb825e3] { background: rgb(34, 36, 38); border-radius: 0.08rem; font-weight: 600; font-family: PingFangSC-Semibold; font-size: 0.22rem; width: 0.36rem; height: 0.36rem; line-height: 0.36rem; display: inline-block; letter-spacing: 0px; } .killsecond-tab .tab-list-multi .scroll-list .tab-item .time .colon[data-v-3fb825e3] { color: rgb(34, 36, 38); width: 0.12rem; font-weight: 600; font-family: PingFangSC-Semibold; font-size: 0.24rem; letter-spacing: 0px; text-align: center; } .killsecond-tab .tab-list-multi .scroll-list .tab-item .status-txt[data-v-3fb825e3] { font-size: 0.24rem; font-family: PingFangSC-Medium; margin-top: 0.12rem; font-weight: 500; text-align: center; line-height: 0.24rem; } .killsecond-tab .tab-list-multi .scroll-list .tab-item.ing[data-v-3fb825e3] { width: 1.36rem; height: 0.84rem; justify-content: flex-start; padding-top: 0.08rem; } .killsecond-tab .tab-list-multi .scroll-list .tab-item.ing .time[data-v-3fb825e3] { display: flex; align-items: center; color: rgb(255, 255, 255); } .killsecond-tab .tab-list-multi .scroll-list .tab-item.ing .status-txt[data-v-3fb825e3] { margin-top: 0.08rem; } .rule-entry-hotarea[data-v-76badf32] { width: 0.75rem; height: 0.4rem; padding: 0px 0px 0px 0.11rem; position: absolute; top: 0px; right: 0.24rem; box-sizing: border-box; } .rule-entry-hotarea .rule-bg[data-v-76badf32] { width: 0.64rem; height: 0.28rem; opacity: 0.32; background: rgb(0, 0, 0); border-radius: 0px 0.24rem 0px 0.16rem; } .rule-entry-hotarea .rule-text[data-v-76badf32] { width: 0.64rem; height: 0.28rem; position: absolute; top: 0px; right: 0px; font-weight: 400; font-family: PingFangSC-Regular; font-size: 0.2rem; color: rgb(255, 255, 255); line-height: 0.28rem; text-align: center; } .component-wrapper[data-v-76badf32] { padding: 0px 0.24rem; position: relative; } .line-wrap[data-v-76badf32] { padding: 0px 0px 0.25rem; border-radius: 0px 0px 0.24rem 0.24rem; margin-top: -0.01rem; overflow: hidden; } .layer-img-container[data-v-76badf32] { font-size: 0px; } .component-wrapper[data-v-9bb33428] { padding: 0px 0.24rem; position: relative; } .chip-field-layout[data-v-c6d46e72] { width: 100%; overflow: hidden; padding-left: 0.2rem; padding-right: 0.2rem; box-sizing: border-box; } .gdsk-transparent { color: transparent !important; } .gdsk-rect { border-radius: 0px; } .gdsk-svg { background: rgb(239, 239, 239) !important; } .gdsk-opacity { opacity: 0 !important; } .gdsk-text-14-2857 { background-size: 100% 0.336rem; background-image: linear-gradient(transparent 14.2857%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 85.7143%, transparent 0%) !important; } .gdsk-text { background-origin: content-box !important; background-clip: content-box !important; background-color: transparent !important; color: transparent !important; background-repeat: repeat-y !important; } .gdsk-text-27-1429 { background-size: 100% 0.7rem; background-image: linear-gradient(transparent 27.1429%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 72.8571%, transparent 0%) !important; } .gdsk-text-20-0000 { background-size: 100% 0.8rem; background-image: linear-gradient(transparent 20%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 80%, transparent 0%) !important; } .gdsk-text-1-7857 { background-size: 100% 0.56rem; background-image: linear-gradient(transparent 1.7857%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 98.2143%, transparent 0%) !important; } .gdsk-text-0-0000 { background-size: 100% 0.24rem; background-image: linear-gradient(transparent 0%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 100%, transparent 0%) !important; } .gdsk-text-3-5714 { background-size: 100% 0.28rem; background-image: linear-gradient(transparent 3.5714%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 96.4286%, transparent 0%) !important; } .gdsk-text--7-1429 { background-size: 100% 0.28rem; background-image: linear-gradient(transparent -7.1429%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 107.143%, transparent 0%) !important; } .gdsk-text-15-6250 { background-size: 100% 0.32rem; background-image: linear-gradient(transparent 15.625%, rgb(238, 238, 238) 0%, rgb(238, 238, 238) 84.375%, transparent 0%) !important; } .gdsk-img-ctn { background: rgb(255, 255, 255) !important; } .gdsk-image { object-fit: cover; background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAAXNSR0IArs4c6QAAAA1JREFUGFdjeP/+/X8ACWsDzWO51SAAAAAASUVORK5CYII=") center center / cover no-repeat !important; } .component-wrapper[data-v-4a9be56b] { z-index: 201; position: relative; } .user-info_bar[data-v-4a9be56b] { display: flex; box-sizing: border-box; width: 100%; height: 0.8rem; padding: 0px 0.24rem 0px 0.28rem; justify-content: space-between; align-items: center; background-repeat: no-repeat; background-position: center center; background-size: 100% 100%; } .user-info_bar.not-show-search[data-v-4a9be56b] { justify-content: space-between; } .user-info_bar .address-box[data-v-4a9be56b] { display: flex; align-items: center; font-size: 0.24rem; color: rgb(26, 26, 26); width: 2.3rem; } .user-info_bar .address-box svg[data-v-4a9be56b] { margin-bottom: 0.02rem; } .user-info_bar .address-box div[data-v-4a9be56b] { overflow: hidden; word-break: keep-all; max-width: 1.92rem; text-overflow: ellipsis; margin-left: 0.04rem; } [data-v-25eeaa14] { -webkit-tap-highlight-color: transparent; } .leftright-container[data-v-25eeaa14] { width: 7.5rem; overflow: hidden; } .leftright-container .leftright-inner-container[data-v-25eeaa14] { position: relative; display: flex; flex-direction: row; transition: all 0.2s ease-in-out 0s; } body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td { margin: 0px; padding: 0px; } body, button, input, select, textarea { font-size: 0.4rem; } ul, ol { list-style: none; } fieldset, img { border: 0px; } body { font-family: "Helvetica, Tahoma, Arial, \"PingFang SC\", \"Hiragino Sans GB\", \"Heiti SC\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\""; } * { -webkit-tap-highlight-color: transparent; }</style></div><section data-v-5fbb6e08="" class="location-bar-container gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" _compname="gdc-wm-location-bar" _ctempname="" id="17492021128630.24363418682416593"></section><section _compname="gdc-waimai-userop-userbar" _ctempname="" id="17492021128630.68003857278868130" class="gdsk-pseudo gdsk-pseudo-circle"><div class="gdsk-pseudo gdsk-pseudo-circle"><section data-v-4a9be56b="" class="component-wrapper gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div data-v-4a9be56b="" class="user-info_bar not-show-search gdsk-transparent gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;https://p0.meituan.net/dptakeaway/aa902271109234f28c68759193d06c2a24434.png&quot;); background-color: transparent;"><div data-v-4a9be56b="" class="address-box gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(43, 43, 43);"><svg data-v-4a9be56b="" width="32" height="32" xmlns="http://www.w3.org/2000/svg" style="zoom: 0.875; width: 0.32rem; height: 0.32rem;" class="gdsk-rect gdsk-svg gdsk-pseudo gdsk-pseudo-circle"></svg><div data-v-4a9be56b="" class="address-text gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.336rem;">福泰中心写字楼</div></div></div></section></div></section><section data-v-5f731be2="" class="component-wrapper gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" _compname="gdc-waimai-userop-loginbox" _ctempname="" id="17492021128630.43542427566244757"></section><section _compname="gdc-fx-skyfall-event-diversion-pop-up" _ctempname="" id="17492021128630.71008232280866360" class="gdsk-pseudo gdsk-pseudo-circle"><div class="gdsk-pseudo gdsk-pseudo-circle"></div></section><section data-v-dad6c396="" class="component-wrapper netunion-red-envelope gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" _compname="gdc-fx-v2-netunion-red-envelope" _ctempname="" id="17492021128630.55696887504935530"><div data-v-dad6c396="" class="new-wangmeng-wrap gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;https://p0.meituan.net/dptakeaway/1cd4db038452706b96b29230da50bdd8107285.png&quot;); background-color: rgb(245, 245, 245); padding-bottom: 0.1rem;"><div data-v-dad6c396="" class="red-bag gdsk-pseudo gdsk-pseudo-circle" style="bottom: 0.1rem;"><div data-v-dad6c396="" class="red-before gdsk-transparent gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;//p0.meituan.net/dptakeaway/87ec7ad3bad3ae935e1a6154665ce4b2606256.png&quot;);"><div data-v-dad6c396="" class="top-title red-ellipsis gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(255, 255, 255);"><span data-v-dad6c396="" class="txt gdsk-transparent gdsk-text-27-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.7rem;">惊喜红包，最高优惠</span><span data-v-dad6c396="" class="x gdsk-transparent gdsk-text-20-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(255, 230, 0); position: relative; background-size: 89.8947% 0.8rem;">288</span><span data-v-dad6c396="" class="txt gdsk-transparent gdsk-text-27-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.7rem;">元</span></div><ul data-v-dad6c396="" class="fake-red-bags gdsk-pseudo gdsk-pseudo-circle"><li data-v-dad6c396="" class="fake-single gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;//p0.meituan.net/dptakeaway/0dfc5aefb458657b389d8de792ddede87824.png&quot;);"><div data-v-dad6c396="" class="fake-name gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 37.3832% 0.28rem; background-position-x: 50%;">惊喜红包</div></li><li data-v-dad6c396="" class="fake-single gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;//p0.meituan.net/dptakeaway/0dfc5aefb458657b389d8de792ddede87824.png&quot;);"><div data-v-dad6c396="" class="fake-name gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 37.3832% 0.28rem; background-position-x: 50%;">惊喜红包</div></li><li data-v-dad6c396="" class="fake-single gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;//p0.meituan.net/dptakeaway/0dfc5aefb458657b389d8de792ddede87824.png&quot;);"><div data-v-dad6c396="" class="fake-name gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 37.3832% 0.28rem; background-position-x: 50%;">惊喜红包</div></li></ul><div data-v-dad6c396="" class="bottom-position-bg-img gdsk-image gdsk-rect gdsk-pseudo gdsk-pseudo-circle" style="background-image: url(&quot;//p1.meituan.net/dptakeaway/069b3c8e4dde66b65ede760bfa28c145393933.png&quot;);"></div><div data-v-dad6c396="" class="receive-btn gdsk-transparent gdsk-pseudo gdsk-pseudo-circle gdsk-image gdsk-rect" style="transform: none;">立即领取</div><div data-v-dad6c396="" class="btn-tip-text gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="transform: none; background-size: 100% 0.308rem; background-position-x: 50%;">随机获得惊喜红包</div></div></div></div></section><section class="gdc-newest-ticket-wall gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" _compname="gdc-gd-newest-ticket-wall" _ctempname="" id="17514664697040.14004937821477448" style="color: rgb(34, 36, 38); background-size: 100% 100%; background-image: url(&quot;&quot;); padding-bottom: 0.08rem;"><div class="ticket-line one-line-2 gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div class="new-ticket-item gdsk-transparent gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background: url(&quot;//p0.meituan.net/dptakeaway/efd2b80a559033921bd35e64c0241f85159145.png&quot;) center center / cover no-repeat;"><div class="left gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div class="p1 gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><span class="unit gdsk-transparent gdsk-opacity gdsk-pseudo gdsk-pseudo-circle">￥</span> <span class="num gdsk-transparent gdsk-text-1-7857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 84.4575% 0.56rem;">15</span></div><div class="p2 gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem;">满15可用</div></div><div class="right gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div class="p1 gdsk-transparent gdsk-text-3-5714 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 83.871% 0.28rem;">专属福利神券</div></div></div><div class="new-ticket-item gdsk-transparent gdsk-img-ctn gdsk-pseudo gdsk-pseudo-circle" style="background: url(&quot;//p0.meituan.net/dptakeaway/efd2b80a559033921bd35e64c0241f85159145.png&quot;) center center / cover no-repeat;"><div class="left gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div class="p1 gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><span class="unit gdsk-transparent gdsk-opacity gdsk-pseudo gdsk-pseudo-circle">￥</span> <span class="num gdsk-transparent gdsk-text-1-7857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 84.4575% 0.56rem;">20</span></div><div class="p2 gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem;">满25可用</div></div><div class="right gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div class="p1 gdsk-transparent gdsk-text-3-5714 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 83.871% 0.28rem;">专属大额神券</div></div></div></div></section><section data-v-9bb33428="" _compname="gdc-wm-sqj-grab-coupon" _ctempname="" id="17516404955830.15255372133206324" class="gdsk-pseudo gdsk-pseudo-circle"><section data-v-76badf32="" data-v-9bb33428="" class="component-wrapper component-wrapper-custom gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="margin-top: 0rem; margin-bottom: 0.24rem;"><div data-v-76badf32="" class="layer-img-container gdsk-pseudo gdsk-pseudo-circle"></div><div data-v-3fb825e3="" data-v-76badf32="" class="killsecond-tab gdsk-pseudo gdsk-pseudo-circle" style="background: rgb(255, 203, 223); color: rgb(34, 36, 38); padding-bottom: 0px;"><div data-v-3fb825e3="" class="tab-list-multi gdsk-pseudo gdsk-pseudo-circle"><div data-v-3fb825e3="" class="scroll-list gdsk-pseudo gdsk-pseudo-circle"><div data-v-3fb825e3="" class="tab-item gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38); margin-right: 0.5rem; width: 1.22rem; opacity: 0.4;"><div data-v-3fb825e3="" class="time gdsk-transparent gdsk-text--7-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.28rem; background-position-x: 50%;">10:00</div><div data-v-3fb825e3="" class="status-txt gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem; background-position-x: 50%;">已结束</div></div><div data-v-3fb825e3="" class="tab-item gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38); margin-right: 0.5rem; width: 1.22rem; opacity: 0.4;"><div data-v-3fb825e3="" class="time gdsk-transparent gdsk-text--7-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.28rem; background-position-x: 50%;">11:00</div><div data-v-3fb825e3="" class="status-txt gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem; background-position-x: 50%;">已结束</div></div><div data-v-3fb825e3="" class="tab-item gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38); margin-right: 0.5rem; width: 1.22rem; opacity: 0.4;"><div data-v-3fb825e3="" class="time gdsk-transparent gdsk-text--7-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.28rem; background-position-x: 50%;">14:00</div><div data-v-3fb825e3="" class="status-txt gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem; background-position-x: 50%;">已结束</div></div><div data-v-3fb825e3="" class="tab-item selected ing gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38); margin-right: 0.5rem; width: 1.36rem; opacity: 1;"><div data-v-3fb825e3="" class="time gdsk-transparent gdsk-text--7-1429 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(255, 255, 255); position: relative; background-size: 100% 0.28rem; background-position-x: 50%;"><span data-v-3fb825e3="" class="time-hms gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="background: 50% / 61.1111% 0.36rem rgb(34, 36, 38); position: relative;">00</span> <span data-v-3fb825e3="" class="colon gdsk-transparent gdsk-opacity gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38);">:</span> <span data-v-3fb825e3="" class="time-hms gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="background: 50% / 61.1111% 0.36rem rgb(34, 36, 38); position: relative;">00</span> <span data-v-3fb825e3="" class="colon gdsk-transparent gdsk-opacity gdsk-pseudo gdsk-pseudo-circle" style="color: rgb(34, 36, 38);">:</span> <span data-v-3fb825e3="" class="time-hms gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" style="background: 50% / 61.1111% 0.36rem rgb(34, 36, 38); position: relative;">00</span></div><div data-v-3fb825e3="" class="status-txt gdsk-transparent gdsk-text-0-0000 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative; background-size: 100% 0.24rem; background-position-x: 50%;">已结束</div></div></div></div></div><div data-v-76badf32="" class="rule-entry-hotarea gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div data-v-76badf32="" class="rule-bg gdsk-pseudo gdsk-pseudo-circle"></div><p data-v-76badf32="" class="rule-text gdsk-transparent gdsk-text-14-2857 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="background-size: 62.5% 0.28rem; background-position-x: 50%;">规则</p></div><div data-v-76badf32="" class="line-wrap gdsk-pseudo gdsk-pseudo-circle" style="background: rgb(255, 203, 223);"><div data-v-0d59191c="" data-v-76badf32="" class="gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><div data-v-0d59191c="" class="no-coupon-container short-line gdsk-transparent gdsk-pseudo gdsk-pseudo-circle"><img data-v-0d59191c="" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" class="no-coupon-pic gdsk-image gdsk-rect gdsk-pseudo gdsk-pseudo-circle" width="200" height="130"><p data-v-0d59191c="" class="no-coupon-tips gdsk-transparent gdsk-text-15-6250 gdsk-text gdsk-pseudo gdsk-pseudo-circle" style="position: relative;">本场没有您可领的红包看看其他优惠吧~</p></div></div></div></section></section><section _compname="gdc-fx-chips-fields-layout" _ctempname="" id="17492021128630.48416936553053680" class="gdsk-pseudo gdsk-pseudo-circle"><div class="gdsk-pseudo gdsk-pseudo-circle"><section data-v-c6d46e72="" class="chip-field-layout gdsk-pseudo gdsk-pseudo-circle" style="padding-top: 0.1rem; padding-bottom: 0rem;"></section></div></section><section data-v-25eeaa14="" class="component-wrapper gdsk-transparent gdsk-pseudo gdsk-pseudo-circle" _compname="gdc-wm-tab-plus" _ctempname="" id="17492021128640.70126503539635100"><section data-v-25eeaa14="" class="leftright-container gdsk-pseudo gdsk-pseudo-circle"><div data-v-25eeaa14="" class="leftright-inner-container gdsk-pseudo gdsk-pseudo-circle" style="width: 0%; transform: none;"></div></section></section>
</div>

<script>
 if(searchParams.wm_opr) {
   window.noPreLoc = true;
 }
</script>

<link rel="stylesheet" type="text/css" href="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/app.17517199600208ff293db616759ea50806f8b5461153d.css" />


<!-- knb & 环境sdk -->
<script crossorigin="anonymous" src="https://s0.meituan.net/bs/knb/v1.7.5:js/knb.js" type="text/javascript" charset="utf-8"></script>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js" type="text/javascript" charset="utf-8"></script>

<!-- 测试环境注入appmock的sdk文件 -->


<!-- 定位js -->
<script>
!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.gdLocationMini=n():t.gdLocationMini=n()}(self,(function(){return t={991:function(t,n,e){"use strict";var o=this&&this.__awaiter||function(t,n,e,o){return new(e||(e=Promise))((function(r,i){function a(t){try{u(o.next(t))}catch(t){i(t)}}function c(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var n;t.done?r(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(a,c)}u((o=o.apply(t,n||[])).next())}))},r=this&&this.__generator||function(t,n){var e,o,r,i,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(e=1,o&&(r=2&c[0]?o.return:c[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,c[1])).done)return r;switch(o=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,o=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){a.label=c[1];break}if(6===c[0]&&a.label<r[1]){a.label=r[1],r=c;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(c);break}r[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(t,a)}catch(t){c=[6,t],o=0}finally{e=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}};n.__esModule=!0,n.preInit=n.preMonitor=n.location=void 0,e(851);var i=e(3260);n.preMonitor=i.preMonitor;var a=e(1820),c=e(7315),u=e(7845),s=e(383),l=e(1470),f=e(6329);if(!window.noPreLoc){var p=new Promise((function(t,n){function e(t){return function(e){(0,i.log)("[locationMini][globalSegmentPromise] reject error with code: ".concat(t),e),i.preMonitor.addError((function(){return{error:{name:"[locationMini] > [globalSegmentPromise] reject error",msg:{errorCode:t,error:e,errorMessage:e&&e.message,errorStack:e&&e.stack}},opts:{category:window.Owl.errorModel.CATEGORY.SCRIPT,level:window.Owl.errorModel.LEVEL.WARN,tags:{code:t,gdId:i.globalData.gdId}}}})),n(e)}}(0,c.gdLocation)().then((function(n){return o(void 0,void 0,void 0,(function(){var o,c,s,l;return r(this,(function(r){switch(r.label){case 0:console.log("[locationMini] 前置定位结束!",Date.now()-i.startTime,n),r.label=1;case 1:return r.trys.push([1,3,,4]),(null===(l=null===(s=null===i.globalData||void 0===i.globalData?void 0:i.globalData.config)||void 0===s?void 0:s.jumpSegmentIdsCheckWhiteList)||void 0===l?void 0:l.includes(null===i.globalData||void 0===i.globalData?void 0:i.globalData.pageId))?(t([]),[2]):[4,(0,u.default)(n)];case 2:return o=r.sent(),Array.isArray(o)?t(o):e(a.ERROR_CODE.SEGMENT_DATA_INVALID)(new Error("segment data invaild with ".concat(JSON.stringify(o)))),console.log("[locationMini] 前置供给结束!",Date.now()-i.startTime,o),[3,4];case 3:return c=r.sent(),e(a.ERROR_CODE.SEGMENT_API_FAILED)(c),console.log("[locationMini] 前置供给异常!",Date.now()-i.startTime,c),[3,4];case 4:return[2]}}))}))})).catch((function(t){console.log("[locationMini] 前置定位异常!",Date.now()-i.startTime,t),e(a.ERROR_CODE.LOCATION_FAILED)(t)}))}));p.catch((function(){})),window.globalSegmentPromise=p}function d(){var t;try{var n=(null===(t=null===i.globalData||void 0===i.globalData?void 0:i.globalData.config)||void 0===t?void 0:t.preKNBWhiteList)||[],e=i.globalData.gdId,o=n.includes(e);i.env.isApp()&&o&&(window.globalKNBLocation=(0,s.default)(!0),window.globalKNBUserInfo=(0,l.default)(),window.globalKNBRegionId=(0,f.default)())}catch(t){console.log("preKNBExec error",t)}}window.preKNBExec=d,d(),window.needPreLoc=!1,n.location=function(t){return window.needPreLoc=!0,(0,c.gdLocation)(t)};var v={isWxInit:i.isWxInit};n.preInit=v},383:function(t,n,e){"use strict";n.__esModule=!0;var o=e(3260);n.default=function(t){var n=(0,o.qs)(),e=n.wm_latitude?n.wm_latitude:"",r=n.wm_longitude?n.wm_longitude:"";return new Promise((function(n,i){(0,o.knbRun)().then((function(a){a.getLocation({sceneToken:"dj-5ba58d2803dd67a9",type:"GCJ02",timeout:6e3,success:function(t){(0,o.log)("[appGetLocation] success",t),window.globalKNBLocationResult=t,n(t)},fail:function(a){(0,o.log)("[appGetLocation] fail",a),window.wmExternalJump&&Number(e)&&Number(r)?n({lat:(0,o.formatLoc)(Number(e)),lng:(0,o.formatLoc)(Number(r))}):t?n(null):i(a)}})}),i).catch(i)}))}},7824:function(t,n,e){"use strict";n.__esModule=!0;var o=e(3260);n.default=function(){return new Promise((function(t,n){o.tencentMap.load().then((function(){try{window.qq&&window.qq.maps||n(new Error("qq not found")),new window.qq.maps.Geolocation("DZYBZ-73WWI-FG6GZ-5JRFR-PNVIE-4OFUL","waimaiapp").getLocation((function(n){t(n)}),n,{timeout:8e3})}catch(t){n(t)}}),n)}))}},2553:function(t,n,e){"use strict";var o,r;n.__esModule=!0;var i=e(1258),a=e(3260),c="local_cache",u=(null===(r=null===(o=null===window||void 0===window?void 0:window.globalData)||void 0===o?void 0:o.config)||void 0===r?void 0:r.locationConfig)||{},s=function(){function t(){this.cacheTime=u.cacheTime||18e4}return t.prototype.shouldGetCache=function(){return!0===u.cache},t.prototype.getCache=function(){if(!this.shouldGetCache())return null;var t=i.default.getItem(c)||{},n=t.expTimeStamp,e=t.value,o=e||{},r=o.lat,u=o.lng;return!(0,a.checkLoc)(e)||n<Date.now()?null:{lat:r,lng:u}},t.prototype.setCache=function(t){if(!(0,a.checkLoc)(t))return!1;var n=Date.now()+this.cacheTime;return i.default.setItem(c,{expTimeStamp:n,value:t})},t}();n.default=new s},7315:function(t,n,e){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},o.apply(this,arguments)};n.__esModule=!0,n.gdLocation=void 0;var r,i,a=e(4107),c=e(3245),u=e(383),s=e(7824),l=e(3260),f=e(2553),p={};!function(t){t.LOCATION_SUCCESS="location_success",t.LOCATION_FAIL="location_fail"}(i||(i={})),n.gdLocation=(new l.NoConcurrent).wrapper((function(t){void 0===t&&(t=p),(0,l.wxInit)();var e=function(t){void 0===t&&(t=p);var e=o(o({},p),t||p),i=e.disableCache;return new Promise((function(t,p){function d(n){f.default.setCache(n),t(n)}function v(t){return function(n){(0,l.log)("[gdLocation] reject source: ".concat(t),n),p(n)}}var g=" catch",h=(0,l.qs)().wm_actual_latitude?(0,l.qs)().wm_actual_latitude:"",m=(0,l.qs)().wm_actual_longitude?(0,l.qs)().wm_actual_longitude:"",w=f.default.getCache();if(!window.wmExternalJump&&l.globalData.loc&&l.globalData.loc.lat&&l.globalData.loc.lng)d(l.globalData.loc);else if(window.wmExternalJump&&Number(h)&&Number(m))d({lat:(0,l.formatLoc)(Number(h)),lng:(0,l.formatLoc)(Number(m))});else if(w&&!i){if(t(w),Date.now()-r<f.default.cacheTime)return;r=Date.now(),setTimeout((function(){(0,n.gdLocation)(o(o({},e),{disableCache:!0}))}),5e3)}else{var y=function(t){if(r=Date.now(),(0,l.log)("coreLocation",Date.now()-l.startTime),l.env.isWeChat||t){var n="wxGetLocation";(0,a.default)().then(d,v(n)).catch(v(n+g))}else l.env.isQQ?(n="qqGetLocation",(0,c.default)(e).then(d,v(n)).catch(v(n+g))):l.env.isApp()?(n="appGetLocation",(0,u.default)().then(d,v(n)).catch(v(n+g))):(n="browserGetLocation",(0,s.default)().then(d,v(n)).catch(v(n+g)))};l.env.isWxMp().then((function(t){y(t)}),(function(){y(!1)})).catch((function(){y(!1)}))}}))},d=new Promise((function(t,n){setTimeout(n,1e4,new Error("Get Location Timeout"))}));return new Promise((function(n,o){Promise.race([e(t),d]).then((function(t){l.preMonitor.addCustomMetric(i.LOCATION_SUCCESS,""),n(t)}),(function(t){var n=t?t.message||t.errMsg||JSON.stringify(t):"";l.preMonitor.addCustomMetric(i.LOCATION_FAIL,n),l.preMonitor.addError((function(){return{error:{name:"[core] > [Location] getLocation Error",msg:n},opts:{category:window.Owl.errorModel.CATEGORY.SCRIPT,level:window.Owl.errorModel.LEVEL.WARN}}})),o(t)}))}))}))},3245:function(t,n){"use strict";n.__esModule=!0,n.default=function(t){var n=window.mqq;return new Promise((function(e,o){try{n.sensor.getLocation({allowCacheTime:t.allowCacheTime},(function(t,n,r){0===t?e({lat:n,lng:r}):o()}))}catch(t){o(t)}}))}},4107:function(t,n,e){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},o.apply(this,arguments)};n.__esModule=!0;var r=e(3260),i=!1;n.default=function(){var t=window.wx;return new Promise((function(n,e){function a(t){return function(n){(0,r.log)("[wxGetLocation] reject source: ".concat(t),n),e(n)}}(0,r.wxRun)("getLocation").then((function e(){if(i)window.setTimeout(e,100);else{i=!0;var c=r.env.isWxHm?{_mt:{sceneToken:"dj-5ba58d2803dd67a9"}}:{};(0,r.log)("[wxGetLocation] wxRun.resolve"),t.getLocation(o(o({type:"gcj02"},c),{success:function(t){i=!1,(0,r.log)("[wxGetLocation] wx.getLocation.success"),n({lat:t.latitude,lng:t.longitude})},fail:function(t){i=!1,a("wx.getLocation.fail")(t)}}))}}),a("wxRun.reject")).catch(a("wxRun.catch"))}))}},9975:function(t,n,e){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},o.apply(this,arguments)};n.__esModule=!0,n.getBaseConfig=void 0;var r=e(3260);n.getBaseConfig=function(){var t=!new RegExp(/(i\.c\.waimai\.test\.sankuai|i\.waimai\.meituan|i\.waimai\.st\.meituan)\.com$/).test(window.location.host),n={url:"/o/activity/segment",method:"POST"};if(n&&!r.globalDataEnv.isLocal&&!t){var e=r.globalDataEnv.isTest?"activity.waimai.test.sankuai.com":r.globalDataEnv.isStage?"marketing.waimai.st.sankuai.com":"marketing.waimai.meituan.com",i="".concat(window.location.protocol,"//").concat(e).concat(n.url);return o(o({},n),{url:i})}return n}},7845:function(t,n,e){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},o.apply(this,arguments)},r=this&&this.__awaiter||function(t,n,e,o){return new(e||(e=Promise))((function(r,i){function a(t){try{u(o.next(t))}catch(t){i(t)}}function c(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var n;t.done?r(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(a,c)}u((o=o.apply(t,n||[])).next())}))},i=this&&this.__generator||function(t,n){var e,o,r,i,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(e=1,o&&(r=2&c[0]?o.return:c[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,c[1])).done)return r;switch(o=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,o=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){a.label=c[1];break}if(6===c[0]&&a.label<r[1]){a.label=r[1],r=c;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(c);break}r[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(t,a)}catch(t){c=[6,t],o=0}finally{e=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}};n.__esModule=!0;var a=e(9975),c=e(332),u=e(4448),s=e(3260),l=e(1924),f=e(3273),p=s.env.isProd?5e3:1e4;n.default=function(t){return r(void 0,void 0,void 0,(function(){var n,e,r,d,v,g,h,m,w,y,b,_;return i(this,(function(i){switch(i.label){case 0:return n=(0,a.getBaseConfig)(),e=(0,c.getSegmentOpts)(t),(0,u.addExtParams)(e),[4,(0,f.getPersonalParams)()];case 1:if(r=i.sent()||{},e=o(o({},e),r),d=n.url,v=e.yy_actual_latitude,g=e.yy_actual_longitude,h=e.wm_latitude,m=e.wm_longitude,w={yy_actual_latitude:v,yy_actual_longitude:g,wm_latitude:h,wm_longitude:m},y={},!s.env.isApp())return[3,5];i.label=2;case 2:return i.trys.push([2,4,,5]),[4,(0,l.gdEncryptRiskData)({data:w,requestUrl:d})];case 3:return(b=i.sent().encryptData)&&(y[l.RISK_HEADER_KEY]=b,delete e.yy_actual_latitude,delete e.yy_actual_longitude,delete e.wm_latitude,delete e.wm_longitude),[3,5];case 4:return _=i.sent(),(0,s.log)(_),[3,5];case 5:return[2,s.ajaxMin.fetch(o(o({},n),{data:(0,u.qsStringify)(e),timeout:p,config:{headers:o(o({},y),{"Content-Type":"application/x-www-form-urlencoded"})}})).then((function(t){var n=t||{},e=n.code,o=n.data;if(0===e&&Array.isArray(o))return window.gundamGlobal||(window.gundamGlobal={}),window.gundamGlobal.validSegmentIds=o,o}))]}}))}))}},332:function(t,n,e){"use strict";var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},o.apply(this,arguments)};n.__esModule=!0,n.getSegmentOpts=void 0;var r=e(3260),i=e(4448);n.getSegmentOpts=function(t){var n,e,a,c,u,s,l=(e=(n=(0,r.qs)()).preview,a=n.wm_latitude,c=n.wm_longitude,e&&"0"===a&&"0"===c?r.globalData.metadata&&r.globalData.metadata.loc:t||{});return o(o(o(o({},(u=(0,r.qs)()||{},s={},(r.globalData.metadata&&r.globalData.metadata.preview||"true"===u.preview)&&(s.preview=1,u.time&&(s.preview_time=u.time)),s)),{activity_id:r.globalData.activityId||"",yy_actual_latitude:(0,i.xLocNumber)(l.lat||0),yy_actual_longitude:(0,i.xLocNumber)(l.lng||0),wm_latitude:(0,i.xLocNumber)(l.lat||0),wm_longitude:(0,i.xLocNumber)(l.lng||0)}),function(t){if(!r.globalDataEnv.isWxMpDh)return{};var n,e=(0,r.qs)()||{},o=e.poiId||"",a=e.poiIdStr||"",c=e.biz_scene_key||"drunkhorse:default",u=e.dh_lat?(0,i.xLocNumber)(e.dh_lat):(0,i.xLocNumber)(t.lat||0),s=e.dh_lng?(0,i.xLocNumber)(e.dh_lng):(0,i.xLocNumber)(t.lng||0);return{poiIdList:o,poiIdStrList:a,biz_scene_key:c,yy_actual_latitude:u,yy_actual_longitude:s,wm_latitude:u,wm_longitude:s,isDrunkHorseNewUser:"string"==typeof(n=e.isDrunkHorseNewUser)?"false"!==n:"boolean"!=typeof n||n}}(l)),(0,i.getGundamV2Params)())}},1298:function(t,n,e){"use strict";n.__esModule=!0;var o=e(5355);n.default=function(t){return function(t){var n=(0,o.default)().outerid;n&&(t.outerid=n)}(t),function(t){var n=(0,o.default)()||{},e=n.wm_ctype,r=function(t){return void 0===t&&(t={}),t.wm_appversion||t.version_name||t.version||t.appVersion||""}(n);e&&!t.wm_ctype&&(t.wm_ctype=e),r&&!t.wm_appversion&&(t.wm_appversion=r)}(t),t}},1924:function(t,n,e){"use strict";n.__esModule=!0,n.gdEncryptRiskData=n.RISK_HEADER_KEY=void 0;var o=e(3260);n.RISK_HEADER_KEY="dj-token",n.gdEncryptRiskData=function(t){var n=t||{},e=n.data,r=n.requestUrl;if(!e||!r){var i=new Error("[gdEncryptRiskData]: data & requestUrl is required.");return Promise.reject(i)}var a=new Promise((function(t,n){setTimeout(n,200,new Error("EncryptRiskData Timeout"))}));return new Promise((function(n,e){var r;Promise.race([(r=t.data,new Promise((function(t,n){function e(t){return function(e){(0,o.log)("[gdEncryptRiskData] reject source: ".concat(t),e),n(e)}}var i=" catch";!function(){if(o.env.isApp()){var n="appEncryptRiskData";(function(t){return new Promise((function(n,e){(0,o.knbRun)().then((function(r){r.use("waimai.djEncryptRiskData",{data:t,success:function(t){var r=(t||{}).encryptData;r&&"string"==typeof r?((0,o.log)("[appEncryptRiskData] success",r),n(r)):e(new Error("[appEncryptRiskData] fail: invalid result - ".concat(JSON.stringify(t))))},fail:function(t){(0,o.log)("[appEncryptRiskData] fail",t),e(t)}})}),e).catch(e)}))})(r).then((function(n){t(n)}),(function(t){e(n)(t)})).catch(e(n+i))}else{var a="otherEncryptRiskData";e(a+i)(new Error("".concat(a," not support now.")))}}()}))),a]).then((function(t){n({encryptData:t})}),(function(t){e(t)}))}))}},3273:function(t,n,e){"use strict";var o=this&&this.__awaiter||function(t,n,e,o){return new(e||(e=Promise))((function(r,i){function a(t){try{u(o.next(t))}catch(t){i(t)}}function c(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var n;t.done?r(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(a,c)}u((o=o.apply(t,n||[])).next())}))},r=this&&this.__generator||function(t,n){var e,o,r,i,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(e=1,o&&(r=2&c[0]?o.return:c[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,c[1])).done)return r;switch(o=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,o=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){a.label=c[1];break}if(6===c[0]&&a.label<r[1]){a.label=r[1],r=c;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(c);break}r[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(t,a)}catch(t){c=[6,t],o=0}finally{e=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}};n.__esModule=!0,n.getPersonalParams=n.getContentSwitch=void 0;var i=e(3260);n.getContentSwitch=function(){var t=function(t,n){i.preMonitor.addError((function(){return{error:{name:t,msg:n&&n.message},opts:{category:window.Owl.errorModel.CATEGORY.SCRIPT,tags:{errorStack:n&&n.stack}}}}))};return new Promise((function(n){return o(void 0,void 0,void 0,(function(){var e,o,a,c,u;return r(this,(function(r){switch(r.label){case 0:if(!i.env.isWmApp)return[3,5];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,(0,i.knbRun)()];case 2:return r.sent().getStorage({key:"waimai_personal_content",success:function(e){try{var o=e.value;o=o||2,n(Number(o))}catch(e){n(2),t("[location-mini -> get-content-switch] > [waimai_personal_content] result parse error",e)}},fail:function(){n(2)}}),[3,4];case 3:return e=r.sent(),n(2),t("[location-mini -> get-content-switch] > [waimai_personal_content] error",e),[3,4];case 4:return[3,11];case 5:if(!i.env.isMtApp)return[3,10];r.label=6;case 6:return r.trys.push([6,8,,9]),[4,(0,i.knbRun)()];case 7:return r.sent().use("meituan.getLawSettings",{success:function(e){try{var o=e.mtPtLawSettings;if(""===o)return void n(2);var r=JSON.parse(o).contentSwitch;n(r?0:1)}catch(e){n(2),t("[location-mini -> get-content-switch] > [meituan.getLawSettings] JSON Parse error",e)}},fail:function(){n(2)}}),[3,9];case 8:return o=r.sent(),n(2),t("[location-mini -> get-content-switch] > [meituan.getLawSettings] error",o),[3,9];case 9:return[3,11];case 10:if(i.env.isWxMpWm)try{if(!(a=(0,i.qs)().privacySwitches))return n(2),[2];if(c=JSON.parse(a),!Array.isArray(c)||!c.length)return n(2),[2];(u=c.find((function(t){return 1===t.type})))&&"number"==typeof u.status&&n(u.status),n(2)}catch(e){n(2),t("[location-mini -> get-content-switch] > [wxMpWm] error",e)}else n(2);r.label=11;case 11:return[2]}}))}))}))},n.getPersonalParams=function(){return o(void 0,void 0,void 0,(function(){var t,e,o,a,c,u;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,n.getContentSwitch)()];case 1:return t=r.sent(),[3,3];case 2:return r.sent(),t=2,[3,3];case 3:return e=(0,i.qs)(),o=e.wmUserIdDeregistration,a=void 0===o?-1:o,c=e.wmUuidDeregistration,u=void 0===c?-1:c,[2,{content_personalized_switch:t,wmUserIdDeregistration:Number(a),wmUuidDeregistration:Number(u)}]}}))}))}},921:function(t,n,e){"use strict";n.__esModule=!0,n.isV2=n.getGundamV2Params=void 0;var o=e(3260);function r(){return o.globalData.gd2}n.getGundamV2Params=function(){return r()?{gdActivityId:o.globalData.gdId,gdPageId:o.globalData.pageId}:{}},n.isV2=r},4448:function(t,n,e){"use strict";var o=this&&this.__createBinding||(Object.create?function(t,n,e,o){void 0===o&&(o=e);var r=Object.getOwnPropertyDescriptor(n,e);r&&!("get"in r?!n.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,o,r)}:function(t,n,e,o){void 0===o&&(o=e),t[o]=n[e]});n.__esModule=!0,n.xLocNumber=n.qsStringify=n.getGundamV2Params=n.addExtParams=void 0,o(n,e(1298),"default","addExtParams"),o(n,e(921),"getGundamV2Params"),o(n,e(2088),"default","qsStringify"),n.xLocNumber=function(t){return n=Boolean("/wzt/preview"===location.pathname),o=(e=new URLSearchParams(location.search)).get("wm_latitude"),r=e.get("wm_longitude"),i=Boolean(o||r),n&&!i?0:t&&t<1e3?parseInt(String(1e6*t),10):parseInt(t,10);var n,e,o,r,i}},2088:function(t,n){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}n.__esModule=!0;var o=function(){for(var t=[],n=0;n<256;++n)t.push("%"+((n<16?"0":"")+n.toString(16)).toUpperCase());return t}(),r=Array.prototype.push,i=function(t,n){r.apply(t,Array.isArray(n)?n:[n])},a=function(t){if(0===t.length)return t;"symbol"===e(t)?t=Symbol.prototype.toString.call(t):"string"!=typeof t&&(t=String(t));for(var n="",r=0;r<t.length;++r){var i=t.charCodeAt(r);45===i||46===i||95===i||126===i||i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122?n+=t.charAt(r):i<128?n+=o[i]:i<2048?n+=o[192|i>>6]+o[128|63&i]:i<55296||i>=57344?n+=o[224|i>>12]+o[128|i>>6&63]+o[128|63&i]:(r+=1,i=65536+((1023&i)<<10|1023&t.charCodeAt(r)),n+=o[240|i>>18]+o[128|i>>12&63]+o[128|i>>6&63]+o[128|63&i])}return n},c=function t(n,o){var r=n;if(r instanceof Date&&(r=Date.prototype.toISOString.call(r)),null===r&&(r=""),"string"==typeof r||"number"==typeof r||"boolean"==typeof r||"symbol"===e(r)||function(t){return!(!t||"object"!==e(t)||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))}(r))return[String(a(o))+"="+String(a(r))];if(void 0===r)return[];for(var c=[],u=Object.keys(r),s=0;s<u.length;++s){var l=u[s];i(c,t(r[l],o+"["+l+"]"))}return c};n.default=function(t){var n=t;if("object"!==e(n)||null===n)return"";for(var o=[],r=Object.keys(n),a=0;a<r.length;++a)i(o,c(n[r[a]],r[a]));return o.join("&").length>0?o.join("&"):""}},1820:function(t,n){"use strict";var e;n.__esModule=!0,n.ERROR_CODE=void 0,(e=n.ERROR_CODE||(n.ERROR_CODE={}))[e.LOCATION_FAILED=1]="LOCATION_FAILED",e[e.SEGMENT_API_FAILED=2]="SEGMENT_API_FAILED",e[e.SEGMENT_DATA_INVALID=3]="SEGMENT_DATA_INVALID"},1470:function(t,n,e){"use strict";n.__esModule=!0;var o=e(3260);n.default=function(){return new Promise((function(t,n){(0,o.knbRun)().then((function(e){try{e.getUserInfo({success:function(n){window.globalKNBUserInfoResult=n,t(n)},fail:function(t){console.log("getKnbUserInfo knb getUserInfo fail",t),n(t)}})}catch(n){t(null)}}),n).catch(n)}))}},6329:function(t,n,e){"use strict";n.__esModule=!0;var o=e(3260);n.default=function(){return new Promise((function(t,n){(0,o.knbRun)().then((function(e){try{e.use("waimai.getRegionId",{key:"mt_super_membership",success:function(n){console.log("getKnbRegionInfo success",n.data);var e=n.data,o={region_id:e.region_id,region_version:e.region_version};window.globalKNBRegionIdResult=o,t(o)},fail:function(n){console.log("getKnbRegionInfo knb getRegionId fail",JSON.stringify(n)),t(null)}})}catch(t){n(t)}}),n).catch(n)}))}},794:function(t,n,e){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}n.__esModule=!0;var r=e(310),i=0,a={jsonp:function(t){return new Promise((function(n,e){var o=r.globalData.config,a=0;o&&o.jsonpRetryTimes&&(a=parseInt(o.jsonpRetryTimes,10)||0);var c=0,u=function(n){(c+=1)>a?(r.preMonitor.addError((function(){return{error:{name:"[ajax.jsonp] jsonp.error",msg:{error:n,errorMessage:n&&n.message,errorStack:n&&n.stack,url:t.url}},opts:{category:window.Owl.errorModel.CATEGORY.AJAX}}})),e(n)):((0,r.log)("[ajax.jsonp] retry time: ",c),s())},s=function(){try{!function(t,n){var e="__jp".concat(i);i+=1;var o,a,c=encodeURIComponent,u=document.getElementsByTagName("script")[0]||document.head;function s(){o.parentNode&&o.parentNode.removeChild(o),window[e]=function(){},a&&clearTimeout(a)}a=setTimeout((function(){s(),n&&n(new Error("Timeout：".concat(6e4)))}),6e4),window[e]=function(t){(0,r.log)("jsonp got",t),s(),n&&n(null,t)},t=(t+="".concat((~t.indexOf("?")?"&":"?")+"callback","=").concat(c(e))).replace("?&","?"),(0,r.log)('jsonp req "%s"',t),(o=document.createElement("script")).src=t,o.onerror=function(){s(),n&&n(new Error("script onerror"))},u.parentNode.insertBefore(o,u)}(t.url,(function(t,e){t?u(t):n(e)}))}catch(t){u(t)}};s()}))},fetch:function(t){return new Promise((function(n,e){var i,a,c;if(!t||"object"!==o(t))throw new Error("fetch参数必须是对象");var u=0,s=r.globalData.config;s&&s.axiosRetryTimes&&(u=parseInt(s.axiosRetryTimes,10)||0);var l=0,f=function(n){(l+=1)>u?(r.preMonitor.addError((function(){return{error:{name:"[ajax.fetch] axios.reject",msg:{rejectReason:n,errorMessage:n&&n.message,errorStack:n&&n.stack,url:t.url}},opts:{category:window.Owl.errorModel.CATEGORY.AJAX,level:window.Owl.errorModel.LEVEL.INFO,tags:{url:t.url}}}})),e(n)):((0,r.log)("[ajax.fetch] retry time: ",l),v())},p=null===(i=null===window||void 0===window?void 0:window.globalData)||void 0===i?void 0:i.gdBs,d=null===(c=null===(a=null===window||void 0===window?void 0:window.globalData)||void 0===a?void 0:a.pageInfo)||void 0===c?void 0:c.pageVersion,v=function(){var o=new XMLHttpRequest,r=t.method,i=void 0===r?"GET":r,a=t.data,c=void 0===a?null:a,u=t.config,s=t.timeout,l=void 0===s?0:s,v=t.url,g=(u||{}).headers;v=function(t,n){var e,o=t.split("?"),r=o[0],i=o[1];return e=i?"".concat(i,"&").concat(Object.keys(n).map((function(t){return"".concat(t,"=").concat(n[t])})).join("&")):Object.keys(n).map((function(t){return"".concat(t,"=").concat(n[t])})).join("&"),"".concat(r,"?").concat(e)}(v,{gdBs:p,pageVersion:d}),o.open(i,v),o.timeout=l,g&&Object.keys(g).forEach((function(t){o.setRequestHeader(t,g[t])})),o.send(c),o.onreadystatechange=function(){if(4===o.readyState)if(200===o.status){var t=void 0;try{t=JSON.parse(o.responseText)}catch(t){e(t)}n(t)}else f(new Error("".concat(i," ").concat(v," ").concat(o.status,": ").concat(o.statusText)))},o.onerror=function(){f(new Error("".concat(i," ").concat(v," network error")))},o.ontimeout=function(){f(new Error("".concat(i," ").concat(v," timeout")))}};v()}))}};n.default=a},310:function(t,n,e){"use strict";var o,r=this&&this.__spreadArray||function(t,n,e){if(e||2===arguments.length)for(var o,r=0,i=n.length;r<i;r++)!o&&r in n||(o||(o=Array.prototype.slice.call(n,0,r)),o[r]=n[r]);return t.concat(o||Array.prototype.slice.call(n))};n.__esModule=!0,n.startTime=n.throttle=n.preMonitor=n.globalData=n.globalDataEnv=n.loadScript=n.formatLoc=n.checkLoc=n.log=void 0;var i=e(5355);n.log=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];(0,i.default)().isDebugMode&&console.log.apply(console,r(["[locationMini]"],t,!1))},n.formatLoc=function(t){return t&&t>1e6?parseFloat(String(t/1e6)):parseFloat(String(t))},n.checkLoc=function(t){var n=t||{},e=n.lat,o=n.lng;return!(!e||!o||"number"!=typeof e||"number"!=typeof o)},n.loadScript=function(t){return new Promise((function(n,e){var o=document.createElement("script");o.src=t,o.crossOrigin="anonymous",o.onload=function(){n()},o.onerror=function(){e(new Error("Failed to load script: ".concat(t)))},document.body.appendChild(o)}))};var a=window.globalData?window.globalData.env:{};n.globalDataEnv=a;var c=window.globalData||{};n.globalData=c,n.throttle=function(t,n){var e=null;return function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];var i=(new Date).getTime();(i-e>n||!e)&&(t.apply(this,o),e=i)}};var u={customMetrics:[],errors:[],addCustomMetric:function(t,n){u.customMetrics.push({name:t,tagValue:n})},addError:function(t){u.errors.push(t)}};n.preMonitor=u;var s=(null===performance||void 0===performance?void 0:performance.timeOrigin)||(null===(o=null===performance||void 0===performance?void 0:performance.timing)||void 0===o?void 0:o.navigationStart)||0;n.startTime=s},4480:function(t,n,e){"use strict";var o=this&&this.__awaiter||function(t,n,e,o){return new(e||(e=Promise))((function(r,i){function a(t){try{u(o.next(t))}catch(t){i(t)}}function c(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var n;t.done?r(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(a,c)}u((o=o.apply(t,n||[])).next())}))},r=this&&this.__generator||function(t,n){var e,o,r,i,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(e=1,o&&(r=2&c[0]?o.return:c[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,c[1])).done)return r;switch(o=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,o=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){a.label=c[1];break}if(6===c[0]&&a.label<r[1]){a.label=r[1],r=c;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(c);break}r[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(t,a)}catch(t){c=[6,t],o=0}finally{e=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}};n.__esModule=!0;var i,a=e(310),c=e(5355),u=String(navigator.userAgent).toLowerCase(),s=["wm_wxapp","sg_wxapp","mt_mp","paotui","thh_wxapp","thh_mt_wxapp","pinhaofan_wxapp","fulishe_wxapp","paotui","wxharmony","mt_wxharmony","dp_wxharmony"],l={isWeChat:u.indexOf("micromessenger")>=0&&u.indexOf("miniprogram mmp")<0,isDp:function(){return!(!a.globalDataEnv.isDpApp&&!a.globalDataEnv.isNewDpApp)||new RegExp(/(dian|51)ping\.com$/).test(window.location.host)},isProd:Boolean(a.globalDataEnv.isProd),isQQ:a.globalDataEnv.isQQ?a.globalDataEnv.isQQ:u.indexOf(" qq/")>=0,isThhApp:u.indexOf("com.meituan.mtmall.inhouse")>-1,isZbApp:Boolean(a.globalDataEnv.isZbApp),isZsApp:Boolean(a.globalDataEnv.isZsApp),isWmApp:Boolean(a.globalDataEnv.isWmApp),isMtApp:Boolean(a.globalDataEnv.isMtApp),isWxMpWm:Boolean(a.globalDataEnv.isWxMpWm),isEmApp:Boolean(a.globalDataEnv.isEmApp),isWxHm:Boolean(a.globalDataEnv.isWxHm),isWxHmWm:Boolean(a.globalDataEnv.isWxHmWm),isWxHmMt:Boolean(a.globalDataEnv.isWxHmMt),isWxHmDp:Boolean(a.globalDataEnv.isWxHmDp),isApp:function(){return!!(a.globalDataEnv.isMtApp||a.globalDataEnv.isDpApp||a.globalDataEnv.isNewDpApp||a.globalDataEnv.isWmApp||l.isThhApp||l.isZbApp||l.isZsApp||l.isEmApp)||function(){var t=window.KNB;if(t){var n=t.env;return(0,a.log)("[env.getEnvFromBridgeKnb] knb env: ".concat(JSON.stringify(n))),Boolean(n.isDPApp||n.isTitans)}return(0,a.log)("[env.getEnvFromBridgeKnb] knb 不存在 Env.ts 149"),!1}()},isWxMp:function(){return o(this,void 0,void 0,(function(){var t;return r(this,(function(n){switch(n.label){case 0:return l.isWeChat?s.includes((0,c.default)().ctype)?[2,Promise.resolve(!0)]:a.globalDataEnv.isWxMp?[2,Promise.resolve(a.globalDataEnv.isWxMp)]:void 0!==i?[3,2]:[4,(e=window.wx,new Promise((function(t){try{e.ready((function(){e.miniProgram.getEnv((function(n){return t(Boolean(n&&n.miniprogram))})),window.setTimeout((function(){t("miniprogram"===window.__wxjs_environment)}),100)}))}catch(n){t(!1)}})))]:[2,Promise.resolve(!1)];case 1:return t=n.sent(),[3,3];case 2:t=i,n.label=3;case 3:return i=t,[2,Promise.resolve(t)]}var e}))}))}};n.default=l},3260:function(t,n,e){"use strict";var o=this&&this.__createBinding||(Object.create?function(t,n,e,o){void 0===o&&(o=e);var r=Object.getOwnPropertyDescriptor(n,e);r&&!("get"in r?!n.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,o,r)}:function(t,n,e,o){void 0===o&&(o=e),t[o]=n[e]});n.__esModule=!0,n.knbRun=n.ajaxMin=n.env=n.NoConcurrent=n.isWxInit=n.wxInit=n.wxRun=n.tencentMap=n.qs=n.startTime=n.throttle=n.preMonitor=n.globalDataEnv=n.globalData=n.formatLoc=n.checkLoc=n.log=void 0;var r=e(310);o(n,r,"log"),o(n,r,"checkLoc"),o(n,r,"formatLoc"),o(n,r,"globalData"),o(n,r,"globalDataEnv"),o(n,r,"preMonitor"),o(n,r,"throttle"),o(n,r,"startTime"),o(n,e(5355),"default","qs"),o(n,e(9445),"default","tencentMap"),o(n,e(5850),"default","wxRun");var i=e(951);o(n,i,"wxInit"),o(n,i,"isWxInit"),o(n,e(9194),"default","NoConcurrent"),o(n,e(4480),"default","env"),o(n,e(794),"default","ajaxMin"),o(n,e(4926),"default","knbRun")},4926:function(t,n,e){"use strict";n.__esModule=!0;var o=e(310);n.default=function(){var t=window.KNB;return new Promise((function(n,e){try{if(t&&"function"==typeof t.ready)t.ready((function(){(0,o.log)("[knbRun] knb.ready"),n(t)}));else{var r="[knbRun] ".concat(t?"KNB.ready方法":"全局对象KNB","不存在");(0,o.log)(r,t),e(new Error(r))}}catch(t){(0,o.log)("[knbRun] catch",t),e(t)}}))}},1258:function(t,n,e){"use strict";n.__esModule=!0;var o=e(310),r=function(){function t(){}return t.getItem=function(t){var n;if(!t)return n;var e=this.storagePrefix+t;try{var o=window.localStorage.getItem(e)||"";n=JSON.parse(o)}catch(t){n=null}return n},t.setItem=function(t,n){if(!t)return!1;var e=this.storagePrefix+t;try{window.localStorage.setItem(e,JSON.stringify(n))}catch(t){return(0,o.log)("localStorage.setItem error:",t),!1}return!0},t.checkIsExist=function(t){return null!==this.getItem(t)},t.storagePrefix="gd_loc_",t}();n.default=r},9194:function(t,n){"use strict";n.__esModule=!0;n.default=function(){var t=this;this.isInvoking=!1,this.result=null,this.wrapper=function(n){return function(e){var o=t;return t.isInvoking&&t.result||(t.isInvoking=!0,t.result=new Promise((function(t,r){n(e).then(o.w(t),o.w(r)).catch(o.w(r))}))),t.result}},this.w=function(n){return function(e){t.isInvoking=!1,t.result=null,n(e)}}}},5355:function(t,n){"use strict";n.__esModule=!0;var e=null;n.default=function(){if(e)return e;for(var t=window.location.search,n={},o=t.slice(t.indexOf("?")+1).split("&"),r=0;r<o.length;r+=1){var i=o[r].split("=");n[i[0]]=decodeURIComponent(i[1])}return e=n}},9445:function(t,n,e){"use strict";n.__esModule=!0;var o=e(310),r=!1,i={load:function(){return new Promise((function(t,n){r?t(null):(0,o.loadScript)("//3gimg.qq.com/lightmap/components/geolocation/geolocation.min.js").then((function(){r=!0,t(null)}),(function(){n(new Error("腾讯地图服务加载失败"))}))}))}};n.default=i},951:function(t,n,e){"use strict";n.__esModule=!0,n.isWxInit=n.wxInit=void 0;var o=e(310),r=e(794),i=e(4480),a=!1;n.isWxInit=a;var c={dp:{test:"tcps.51ping.com",prod:"cps.dianping.com"},mt:{test:"m.sh.waimai.test.sankuai.com",prod:"i.meituan.com"}},u=["checkJsApi","onMenuShareTimeline","onMenuShareAppMessage","onMenuShareQQ","updateTimelineShareData","updateAppMessageShareData","onMenuShareWeibo","onMenuShareQZone","startRecord","stopRecord","onVoiceRecordEnd","playVoice","pauseVoice","stopVoice","onVoicePlayEnd","uploadVoice","downloadVoice","chooseImage","previewImage","uploadImage","downloadImage","translateVoice","getNetworkType","openLocation","getLocation","hideOptionMenu","showOptionMenu","hideMenuItems","showMenuItems","hideAllNonBaseMenuItem","showAllNonBaseMenuItem","closeWindow","scanQRCode","chooseWXPay","openProductSpecificView","addCard","chooseCard","openCard","launch3rdApp","launchApplication"];n.wxInit=function(){a||i.default.isWeChat&&function(){n.isWxInit=a=!0;var t=i.default.isDp();new Promise((function(n,e){var a=t?c.dp:c.mt,s=t?"/weixin/config.js":"/firework/api/weixin/config.json",l="//".concat(a.prod).concat(s);i.default.isWxHm?n():t?(l="//".concat(i.default.isProd?a.prod:a.test).concat(s),(0,o.loadScript)("".concat(l,"?_=").concat(Date.now(),"&apis=").concat(u.toString(),"&debug=false")).then(n)):i.default.isProd?(l+="?url=".concat(encodeURIComponent(location.href)),r.default.jsonp({url:l}).then((function(t){var n=window.wx;0===t.status&&n&&"function"==typeof n.config?n.config(function(t){return{debug:!1,beta:!0,appId:"wxc72f01f43da0083b",timestamp:t.timestamp,nonceStr:t.nonceStr,signature:t.signature,jsApiList:u}}(t.data||{})):e()}),e).catch(e)):n()}))}()}},5850:function(t,n,e){"use strict";n.__esModule=!0;var o=e(310);n.default=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=window.wx;(0,o.log)("[wxRun] entry");var r=t.concat([]);return new Promise((function(n,i){try{(0,o.log)("[wxRun] try"),e.ready((function(){(0,o.log)("[wxRun] wx.ready"),e.checkJsApi({jsApiList:r,success:function(e){(0,o.log)("[wxRun] wx.checkJsApi.success",e),e&&e.checkResult?1===t.length?e.checkResult[t[0]]?((0,o.log)("[wxRun] wx.checkJsApi.success.resolve","case 1"),n()):((0,o.log)("[wxRun] wx.checkJsApi.success.reject","case 1"),i(new Error("wx checkJsApi success without api: ".concat(t[0])))):((0,o.log)("[wxRun] wx.checkJsApi.success.resolve","case 2"),n(e.checkResult)):((0,o.log)("[wxRun] wx.checkJsApi.success.reject","case 2"),i(new Error("wx checkJsApi success without res")))},fail:function(t){(0,o.log)("[wxRun] wx.checkJsApi.fail.reject",t),i(new Error("wx checkJsApi fail"))}})}))}catch(t){(0,o.log)("[wxRun] catch",t),i(t)}}))}},4963:function(t){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},3328:function(t){t.exports=function(t,n,e,o){if(!(t instanceof n)||void 0!==o&&o in t)throw TypeError(e+": incorrect invocation!");return t}},7007:function(t,n,e){var o=e(5286);t.exports=function(t){if(!o(t))throw TypeError(t+" is not an object!");return t}},1488:function(t,n,e){var o=e(2032),r=e(6314)("toStringTag"),i="Arguments"==o(function(){return arguments}());t.exports=function(t){var n,e,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=Object(t),r))?e:i?o(n):"Object"==(a=o(n))&&"function"==typeof n.callee?"Arguments":a}},2032:function(t){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},5645:function(t){var n=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},741:function(t,n,e){var o=e(4963);t.exports=function(t,n,e){if(o(t),void 0===n)return t;switch(e){case 1:return function(e){return t.call(n,e)};case 2:return function(e,o){return t.call(n,e,o)};case 3:return function(e,o,r){return t.call(n,e,o,r)}}return function(){return t.apply(n,arguments)}}},7057:function(t,n,e){t.exports=!e(4253)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},2457:function(t,n,e){var o=e(5286),r=e(3816).document,i=o(r)&&o(r.createElement);t.exports=function(t){return i?r.createElement(t):{}}},2985:function(t,n,e){var o=e(3816),r=e(5645),i=e(7728),a=e(7234),c=e(741),u="prototype",s=function(t,n,e){var l,f,p,d,v=t&s.F,g=t&s.G,h=t&s.S,m=t&s.P,w=t&s.B,y=g?o:h?o[n]||(o[n]={}):(o[n]||{})[u],b=g?r:r[n]||(r[n]={}),_=b[u]||(b[u]={});for(l in g&&(e=n),e)p=((f=!v&&y&&void 0!==y[l])?y:e)[l],d=w&&f?c(p,o):m&&"function"==typeof p?c(Function.call,p):p,y&&a(y,l,p,t&s.U),b[l]!=p&&i(b,l,d),m&&_[l]!=p&&(_[l]=p)};o.core=r,s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,t.exports=s},4253:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},3531:function(t,n,e){var o=e(741),r=e(8851),i=e(6555),a=e(7007),c=e(875),u=e(9002),s={},l={},f=t.exports=function(t,n,e,f,p){var d,v,g,h,m=p?function(){return t}:u(t),w=o(e,f,n?2:1),y=0;if("function"!=typeof m)throw TypeError(t+" is not iterable!");if(i(m)){for(d=c(t.length);d>y;y++)if((h=n?w(a(v=t[y])[0],v[1]):w(t[y]))===s||h===l)return h}else for(g=m.call(t);!(v=g.next()).done;)if((h=r(g,w,v.value,n))===s||h===l)return h};f.BREAK=s,f.RETURN=l},18:function(t,n,e){t.exports=e(3825)("native-function-to-string",Function.toString)},3816:function(t){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},9181:function(t){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},7728:function(t,n,e){var o=e(9275),r=e(681);t.exports=e(7057)?function(t,n,e){return o.f(t,n,r(1,e))}:function(t,n,e){return t[n]=e,t}},639:function(t,n,e){var o=e(3816).document;t.exports=o&&o.documentElement},1734:function(t,n,e){t.exports=!e(7057)&&!e(4253)((function(){return 7!=Object.defineProperty(e(2457)("div"),"a",{get:function(){return 7}}).a}))},7242:function(t){t.exports=function(t,n,e){var o=void 0===e;switch(n.length){case 0:return o?t():t.call(e);case 1:return o?t(n[0]):t.call(e,n[0]);case 2:return o?t(n[0],n[1]):t.call(e,n[0],n[1]);case 3:return o?t(n[0],n[1],n[2]):t.call(e,n[0],n[1],n[2]);case 4:return o?t(n[0],n[1],n[2],n[3]):t.call(e,n[0],n[1],n[2],n[3])}return t.apply(e,n)}},6555:function(t,n,e){var o=e(2803),r=e(6314)("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||i[r]===t)}},5286:function(t){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},8851:function(t,n,e){var o=e(7007);t.exports=function(t,n,e,r){try{return r?n(o(e)[0],e[1]):n(e)}catch(n){var i=t.return;throw void 0!==i&&o(i.call(t)),n}}},7462:function(t,n,e){var o=e(6314)("iterator"),r=!1;try{var i=[7][o]();i.return=function(){r=!0},Array.from(i,(function(){throw 2}))}catch(t){}t.exports=function(t,n){if(!n&&!r)return!1;var e=!1;try{var i=[7],a=i[o]();a.next=function(){return{done:e=!0}},i[o]=function(){return a},t(i)}catch(t){}return e}},2803:function(t){t.exports={}},4461:function(t){t.exports=!1},4351:function(t,n,e){var o=e(3816),r=e(4193).set,i=o.MutationObserver||o.WebKitMutationObserver,a=o.process,c=o.Promise,u="process"==e(2032)(a);t.exports=function(){var t,n,e,s=function(){var o,r;for(u&&(o=a.domain)&&o.exit();t;){r=t.fn,t=t.next;try{r()}catch(o){throw t?e():n=void 0,o}}n=void 0,o&&o.enter()};if(u)e=function(){a.nextTick(s)};else if(!i||o.navigator&&o.navigator.standalone)if(c&&c.resolve){var l=c.resolve(void 0);e=function(){l.then(s)}}else e=function(){r.call(o,s)};else{var f=!0,p=document.createTextNode("");new i(s).observe(p,{characterData:!0}),e=function(){p.data=f=!f}}return function(o){var r={fn:o,next:void 0};n&&(n.next=r),t||(t=r,e()),n=r}}},3499:function(t,n,e){"use strict";var o=e(4963);function r(t){var n,e;this.promise=new t((function(t,o){if(void 0!==n||void 0!==e)throw TypeError("Bad Promise constructor");n=t,e=o})),this.resolve=o(n),this.reject=o(e)}t.exports.f=function(t){return new r(t)}},9275:function(t,n,e){var o=e(7007),r=e(1734),i=e(1689),a=Object.defineProperty;n.f=e(7057)?Object.defineProperty:function(t,n,e){if(o(t),n=i(n,!0),o(e),r)try{return a(t,n,e)}catch(t){}if("get"in e||"set"in e)throw TypeError("Accessors not supported!");return"value"in e&&(t[n]=e.value),t}},188:function(t){t.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},94:function(t,n,e){var o=e(7007),r=e(5286),i=e(3499);t.exports=function(t,n){if(o(t),r(n)&&n.constructor===t)return n;var e=i.f(t);return(0,e.resolve)(n),e.promise}},681:function(t){t.exports=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}}},4408:function(t,n,e){var o=e(7234);t.exports=function(t,n,e){for(var r in n)o(t,r,n[r],e);return t}},7234:function(t,n,e){var o=e(3816),r=e(7728),i=e(9181),a=e(3953)("src"),c=e(18),u="toString",s=(""+c).split(u);e(5645).inspectSource=function(t){return c.call(t)},(t.exports=function(t,n,e,c){var u="function"==typeof e;u&&(i(e,"name")||r(e,"name",n)),t[n]!==e&&(u&&(i(e,a)||r(e,a,t[n]?""+t[n]:s.join(String(n)))),t===o?t[n]=e:c?t[n]?t[n]=e:r(t,n,e):(delete t[n],r(t,n,e)))})(Function.prototype,u,(function(){return"function"==typeof this&&this[a]||c.call(this)}))},2974:function(t,n,e){"use strict";var o=e(3816),r=e(9275),i=e(7057),a=e(6314)("species");t.exports=function(t){var n=o[t];i&&n&&!n[a]&&r.f(n,a,{configurable:!0,get:function(){return this}})}},2943:function(t,n,e){var o=e(9275).f,r=e(9181),i=e(6314)("toStringTag");t.exports=function(t,n,e){t&&!r(t=e?t:t.prototype,i)&&o(t,i,{configurable:!0,value:n})}},3825:function(t,n,e){var o=e(5645),r=e(3816),i="__core-js_shared__",a=r[i]||(r[i]={});(t.exports=function(t,n){return a[t]||(a[t]=void 0!==n?n:{})})("versions",[]).push({version:o.version,mode:e(4461)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},8364:function(t,n,e){var o=e(7007),r=e(4963),i=e(6314)("species");t.exports=function(t,n){var e,a=o(t).constructor;return void 0===a||null==(e=o(a)[i])?n:r(e)}},4193:function(t,n,e){var o,r,i,a=e(741),c=e(7242),u=e(639),s=e(2457),l=e(3816),f=l.process,p=l.setImmediate,d=l.clearImmediate,v=l.MessageChannel,g=l.Dispatch,h=0,m={},w="onreadystatechange",y=function(){var t=+this;if(m.hasOwnProperty(t)){var n=m[t];delete m[t],n()}},b=function(t){y.call(t.data)};p&&d||(p=function(t){for(var n=[],e=1;arguments.length>e;)n.push(arguments[e++]);return m[++h]=function(){c("function"==typeof t?t:Function(t),n)},o(h),h},d=function(t){delete m[t]},"process"==e(2032)(f)?o=function(t){f.nextTick(a(y,t,1))}:g&&g.now?o=function(t){g.now(a(y,t,1))}:v?(i=(r=new v).port2,r.port1.onmessage=b,o=a(i.postMessage,i,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(o=function(t){l.postMessage(t+"","*")},l.addEventListener("message",b,!1)):o=w in s("script")?function(t){u.appendChild(s("script"))[w]=function(){u.removeChild(this),y.call(t)}}:function(t){setTimeout(a(y,t,1),0)}),t.exports={set:p,clear:d}},1467:function(t){var n=Math.ceil,e=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?e:n)(t)}},875:function(t,n,e){var o=e(1467),r=Math.min;t.exports=function(t){return t>0?r(o(t),9007199254740991):0}},1689:function(t,n,e){var o=e(5286);t.exports=function(t,n){if(!o(t))return t;var e,r;if(n&&"function"==typeof(e=t.toString)&&!o(r=e.call(t)))return r;if("function"==typeof(e=t.valueOf)&&!o(r=e.call(t)))return r;if(!n&&"function"==typeof(e=t.toString)&&!o(r=e.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},3953:function(t){var n=0,e=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+e).toString(36))}},575:function(t,n,e){var o=e(3816).navigator;t.exports=o&&o.userAgent||""},6314:function(t,n,e){var o=e(3825)("wks"),r=e(3953),i=e(3816).Symbol,a="function"==typeof i;(t.exports=function(t){return o[t]||(o[t]=a&&i[t]||(a?i:r)("Symbol."+t))}).store=o},9002:function(t,n,e){var o=e(1488),r=e(6314)("iterator"),i=e(2803);t.exports=e(5645).getIteratorMethod=function(t){if(null!=t)return t[r]||t["@@iterator"]||i[o(t)]}},851:function(t,n,e){"use strict";var o,r,i,a,c=e(4461),u=e(3816),s=e(741),l=e(1488),f=e(2985),p=e(5286),d=e(4963),v=e(3328),g=e(3531),h=e(8364),m=e(4193).set,w=e(4351)(),y=e(3499),b=e(188),_=e(575),x=e(94),E="Promise",D=u.TypeError,S=u.process,M=S&&S.versions,A=M&&M.v8||"",k=u[E],R="process"==l(S),I=function(){},O=r=y.f,P=!!function(){try{var t=k.resolve(1),n=(t.constructor={})[e(6314)("species")]=function(t){t(I,I)};return(R||"function"==typeof PromiseRejectionEvent)&&t.then(I)instanceof n&&0!==A.indexOf("6.6")&&-1===_.indexOf("Chrome/66")}catch(t){}}(),L=function(t){var n;return!(!p(t)||"function"!=typeof(n=t.then))&&n},j=function(t,n){if(!t._n){t._n=!0;var e=t._c;w((function(){for(var o=t._v,r=1==t._s,i=0,a=function(n){var e,i,a,c=r?n.ok:n.fail,u=n.resolve,s=n.reject,l=n.domain;try{c?(r||(2==t._h&&C(t),t._h=1),!0===c?e=o:(l&&l.enter(),e=c(o),l&&(l.exit(),a=!0)),e===n.promise?s(D("Promise-chain cycle")):(i=L(e))?i.call(e,u,s):u(e)):s(o)}catch(t){l&&!a&&l.exit(),s(t)}};e.length>i;)a(e[i++]);t._c=[],t._n=!1,n&&!t._h&&T(t)}))}},T=function(t){m.call(u,(function(){var n,e,o,r=t._v,i=N(t);if(i&&(n=b((function(){R?S.emit("unhandledRejection",r,t):(e=u.onunhandledrejection)?e({promise:t,reason:r}):(o=u.console)&&o.error&&o.error("Unhandled promise rejection",r)})),t._h=R||N(t)?2:1),t._a=void 0,i&&n.e)throw n.v}))},N=function(t){return 1!==t._h&&0===(t._a||t._c).length},C=function(t){m.call(u,(function(){var n;R?S.emit("rejectionHandled",t):(n=u.onrejectionhandled)&&n({promise:t,reason:t._v})}))},W=function(t){var n=this;n._d||(n._d=!0,(n=n._w||n)._v=t,n._s=2,n._a||(n._a=n._c.slice()),j(n,!0))},B=function(t){var n,e=this;if(!e._d){e._d=!0,e=e._w||e;try{if(e===t)throw D("Promise can't be resolved itself");(n=L(t))?w((function(){var o={_w:e,_d:!1};try{n.call(t,s(B,o,1),s(W,o,1))}catch(t){W.call(o,t)}})):(e._v=t,e._s=1,j(e,!1))}catch(t){W.call({_w:e,_d:!1},t)}}};P||(k=function(t){v(this,k,E,"_h"),d(t),o.call(this);try{t(s(B,this,1),s(W,this,1))}catch(t){W.call(this,t)}},(o=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=e(4408)(k.prototype,{then:function(t,n){var e=O(h(this,k));return e.ok="function"!=typeof t||t,e.fail="function"==typeof n&&n,e.domain=R?S.domain:void 0,this._c.push(e),this._a&&this._a.push(e),this._s&&j(this,!1),e.promise},catch:function(t){return this.then(void 0,t)}}),i=function(){var t=new o;this.promise=t,this.resolve=s(B,t,1),this.reject=s(W,t,1)},y.f=O=function(t){return t===k||t===a?new i(t):r(t)}),f(f.G+f.W+f.F*!P,{Promise:k}),e(2943)(k,E),e(2974)(E),a=e(5645)[E],f(f.S+f.F*!P,E,{reject:function(t){var n=O(this);return(0,n.reject)(t),n.promise}}),f(f.S+f.F*(c||!P),E,{resolve:function(t){return x(c&&this===a?k:this,t)}}),f(f.S+f.F*!(P&&e(7462)((function(t){k.all(t).catch(I)}))),E,{all:function(t){var n=this,e=O(n),o=e.resolve,r=e.reject,i=b((function(){var e=[],i=0,a=1;g(t,!1,(function(t){var c=i++,u=!1;e.push(void 0),a++,n.resolve(t).then((function(t){u||(u=!0,e[c]=t,--a||o(e))}),r)})),--a||o(e)}));return i.e&&r(i.v),e.promise},race:function(t){var n=this,e=O(n),o=e.reject,r=b((function(){g(t,!1,(function(t){n.resolve(t).then(e.resolve,o)}))}));return r.e&&o(r.v),e.promise}})}},n={},function e(o){var r=n[o];if(void 0!==r)return r.exports;var i=n[o]={exports:{}};return t[o].call(i.exports,i,i.exports,e),i.exports}(991);var t,n}));
</script>

<script crossorigin="anonymous" src="https://s3.meituan.net/v1/mss_eb9ea9cfff9840198c3ae909b17b4270/production/logan-websdk/logan_2.3.4.js" type="text/javascript" charset="utf-8"></script>
<script crossorigin="anonymous" src="https://s3.meituan.net/v1/mss_eb9ea9cfff9840198c3ae909b17b4270/production/owl/gd.js" type="text/javascript" charset="utf-8"></script>
<!-- gdOwl，监控补丁 -->
<script>
  try {
    // owl-patch代码，需要前置执行
    !function(t,n){if("object"==typeof exports&&"object"==typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var r=n();for(var e in r)("object"==typeof exports?exports:t)[e]=r[e]}}(self,(function(){return function(){var t={963:function(t){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},7:function(t,n,r){var e=r(286);t.exports=function(t){if(!e(t))throw TypeError(t+" is not an object!");return t}},315:function(t,n,r){var e=r(110),o=r(875),i=r(337);t.exports=function(t){return function(n,r,c){var a,u=e(n),s=o(u.length),l=i(c,s);if(t&&r!=r){for(;s>l;)if((a=u[l++])!=a)return!0}else for(;s>l;l++)if((t||l in u)&&u[l]===r)return t||l||0;return!t&&-1}}},50:function(t,n,r){var e=r(741),o=r(797),i=r(508),c=r(875),a=r(886);t.exports=function(t,n){var r=1==t,u=2==t,s=3==t,l=4==t,f=6==t,d=5==t||f,p=n||a;return function(n,a,v){for(var h,w,g=i(n),y=o(g),m=e(a,v,3),O=c(y.length),b=0,x=r?p(n,O):u?p(n,0):void 0;O>b;b++)if((d||b in y)&&(w=m(h=y[b],b,g),t))if(r)x[b]=w;else if(w)switch(t){case 3:return!0;case 5:return h;case 6:return b;case 2:x.push(h)}else if(l)return!1;return f?-1:s||l?l:x}}},736:function(t,n,r){var e=r(286),o=r(302),i=r(314)("species");t.exports=function(t){var n;return o(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!o(n.prototype)||(n=void 0),e(n)&&null===(n=n[i])&&(n=void 0)),void 0===n?Array:n}},886:function(t,n,r){var e=r(736);t.exports=function(t,n){return new(e(t))(n)}},32:function(t){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},645:function(t){var n=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},741:function(t,n,r){var e=r(963);t.exports=function(t,n,r){if(e(t),void 0===n)return t;switch(r){case 1:return function(r){return t.call(n,r)};case 2:return function(r,e){return t.call(n,r,e)};case 3:return function(r,e,o){return t.call(n,r,e,o)}}return function(){return t.apply(n,arguments)}}},355:function(t){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},57:function(t,n,r){t.exports=!r(253)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},457:function(t,n,r){var e=r(286),o=r(816).document,i=e(o)&&e(o.createElement);t.exports=function(t){return i?o.createElement(t):{}}},430:function(t){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},985:function(t,n,r){var e=r(816),o=r(645),i=r(728),c=r(234),a=r(741),u="prototype",s=function(t,n,r){var l,f,d,p,v=t&s.F,h=t&s.G,w=t&s.S,g=t&s.P,y=t&s.B,m=h?e:w?e[n]||(e[n]={}):(e[n]||{})[u],O=h?o:o[n]||(o[n]={}),b=O[u]||(O[u]={});for(l in h&&(r=n),r)d=((f=!v&&m&&void 0!==m[l])?m:r)[l],p=y&&f?a(d,e):g&&"function"==typeof d?a(Function.call,d):d,m&&c(m,l,d,t&s.U),O[l]!=d&&i(O,l,p),g&&b[l]!=d&&(b[l]=d)};e.core=o,s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,t.exports=s},253:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},18:function(t,n,r){t.exports=r(825)("native-function-to-string",Function.toString)},816:function(t){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},181:function(t){var n={}.hasOwnProperty;t.exports=function(t,r){return n.call(t,r)}},728:function(t,n,r){var e=r(275),o=r(681);t.exports=r(57)?function(t,n,r){return e.f(t,n,o(1,r))}:function(t,n,r){return t[n]=r,t}},734:function(t,n,r){t.exports=!r(57)&&!r(253)((function(){return 7!=Object.defineProperty(r(457)("div"),"a",{get:function(){return 7}}).a}))},797:function(t,n,r){var e=r(32);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==e(t)?t.split(""):Object(t)}},302:function(t,n,r){var e=r(32);t.exports=Array.isArray||function(t){return"Array"==e(t)}},286:function(t){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},461:function(t){t.exports=!1},275:function(t,n,r){var e=r(7),o=r(734),i=r(689),c=Object.defineProperty;n.f=r(57)?Object.defineProperty:function(t,n,r){if(e(t),n=i(n,!0),e(r),o)try{return c(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(t[n]=r.value),t}},189:function(t,n,r){var e=r(181),o=r(110),i=r(315)(!1),c=r(335)("IE_PROTO");t.exports=function(t,n){var r,a=o(t),u=0,s=[];for(r in a)r!=c&&e(a,r)&&s.push(r);for(;n.length>u;)e(a,r=n[u++])&&(~i(s,r)||s.push(r));return s}},184:function(t,n,r){var e=r(189),o=r(430);t.exports=Object.keys||function(t){return e(t,o)}},160:function(t,n,r){var e=r(985),o=r(645),i=r(253);t.exports=function(t,n){var r=(o.Object||{})[t]||Object[t],c={};c[t]=n(r),e(e.S+e.F*i((function(){r(1)})),"Object",c)}},681:function(t){t.exports=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}}},234:function(t,n,r){var e=r(816),o=r(728),i=r(181),c=r(953)("src"),a=r(18),u="toString",s=(""+a).split(u);r(645).inspectSource=function(t){return a.call(t)},(t.exports=function(t,n,r,a){var u="function"==typeof r;u&&(i(r,"name")||o(r,"name",n)),t[n]!==r&&(u&&(i(r,c)||o(r,c,t[n]?""+t[n]:s.join(String(n)))),t===e?t[n]=r:a?t[n]?t[n]=r:o(t,n,r):(delete t[n],o(t,n,r)))})(Function.prototype,u,(function(){return"function"==typeof this&&this[c]||a.call(this)}))},335:function(t,n,r){var e=r(825)("keys"),o=r(953);t.exports=function(t){return e[t]||(e[t]=o(t))}},825:function(t,n,r){var e=r(645),o=r(816),i="__core-js_shared__",c=o[i]||(o[i]={});(t.exports=function(t,n){return c[t]||(c[t]=void 0!==n?n:{})})("versions",[]).push({version:e.version,mode:r(461)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},717:function(t,n,r){"use strict";var e=r(253);t.exports=function(t,n){return!!t&&e((function(){n?t.call(null,(function(){}),1):t.call(null)}))}},337:function(t,n,r){var e=r(467),o=Math.max,i=Math.min;t.exports=function(t,n){return(t=e(t))<0?o(t+n,0):i(t,n)}},467:function(t){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},110:function(t,n,r){var e=r(797),o=r(355);t.exports=function(t){return e(o(t))}},875:function(t,n,r){var e=r(467),o=Math.min;t.exports=function(t){return t>0?o(e(t),9007199254740991):0}},508:function(t,n,r){var e=r(355);t.exports=function(t){return Object(e(t))}},689:function(t,n,r){var e=r(286);t.exports=function(t,n){if(!e(t))return t;var r,o;if(n&&"function"==typeof(r=t.toString)&&!e(o=r.call(t)))return o;if("function"==typeof(r=t.valueOf)&&!e(o=r.call(t)))return o;if(!n&&"function"==typeof(r=t.toString)&&!e(o=r.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},953:function(t){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},314:function(t,n,r){var e=r(825)("wks"),o=r(953),i=r(816).Symbol,c="function"==typeof i;(t.exports=function(t){return e[t]||(e[t]=c&&i[t]||(c?i:o)("Symbol."+t))}).store=e},837:function(t,n,r){"use strict";var e=r(985),o=r(50)(2);e(e.P+e.F*!r(717)([].filter,!0),"Array",{filter:function(t){return o(this,t,arguments[1])}})},371:function(t,n,r){"use strict";var e=r(985),o=r(50)(1);e(e.P+e.F*!r(717)([].map,!0),"Array",{map:function(t){return o(this,t,arguments[1])}})},773:function(t,n,r){"use strict";var e=r(985),o=r(50)(3);e(e.P+e.F*!r(717)([].some,!0),"Array",{some:function(t){return o(this,t,arguments[1])}})},476:function(t,n,r){var e=r(508),o=r(184);r(160)("keys",(function(){return function(t){return o(e(t))}}))}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.d=function(t,n){for(var e in n)r.o(n,e)&&!r.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:n[e]})},r.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var e={};return function(){"use strict";r.r(e),r.d(e,{default:function(){return M}}),r(773),r(371),r(837),r(476);var t=function(t,n,r){if(r||2===arguments.length)for(var e,o=0,i=n.length;o<i;o++)!e&&o in n||(e||(e=Array.prototype.slice.call(n,0,o)),e[o]=n[o]);return t.concat(e||Array.prototype.slice.call(n))},n=null;function o(){return n||(r=function(){var t,n;return null===(n=null===(t=null===window||void 0===window?void 0:window.globalData)||void 0===t?void 0:t.env)||void 0===n?void 0:n.isTest},n={log:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r()&&console.log.apply(console,t(["【gundam owl】"],n,!1))},warn:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r()&&console.warn.apply(console,t(["【【gundam owl】"],n,!1))},error:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];n.map((function(t){var n;return null===(n=null===window||void 0===window?void 0:window.gdOwl)||void 0===n?void 0:n.addNormalError(t)})),console.error.apply(console,t(["【gundam owl】"],n,!1))}},n);var r}function i(t,n){var r=o();try{t()}catch(t){r.error(t)}finally{n&&n()}}var c,a=function(){return null},u="【gdOwl White】",s={tuanhaohuo:[37],shangou:[13,59],waimai:[3,5,10,22,26,33],fenxiao:[43],dh:[54],yiyao:[56]},l={tuanhaohuo:[44,51],shangou:[2,60,71],waimai:[1,5,6,9,10,11,12,13,14,16,21,22,23,25,26,27,29,30,31,34,38,39,40,41,42,43,50,52,55,56,62,63,64,73],fenxiao:[48],dh:[59],yiyao:[57,58,70]},f={lx:{reg:/https?:\/\/(postreport|plx|lx1|lx2).meituan.(com|net)\/\?_lxsdk_rnd=/,success:"lx_success",fail:"lx_error"},logan:{reg:/https?:\/\/logan.sankuai.com/,success:"logan_success",fail:"logan_error"}},d={SUCCESS:/^[2,3]/},p=function(){return p=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++)for(var o in n=arguments[r])Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o]);return t},p.apply(this,arguments)},v=function(){function t(t,n){void 0===n&&(n="#app"),this.domeID=n,this.isWhite=!1,this._intervalLimit=4,this._interval=0,this._gdOwl=t,this.errors=[],this.isUpload=!1,this._init()}return t.prototype._init=function(){var t=this;window.addEventListener("unload",this._upload.bind(this)),window.addEventListener("pagehide",this._upload.bind(this)),window.addEventListener("visibilitychange",(function(){"visible"!==document.visibilityState&&t._upload()})),this._interval=window.setInterval((function(){t._gdOwl.logService.log("white interval check"),t.updateWhiteStatus(),t._intervalLimit-=1,t._intervalLimit<=0&&(t._upload(),t._gdOwl.logService.log("white interval check end"),clearInterval(t._interval))}),1e3),window.addEventListener("error",this.errorHandler.bind(this)),window.addEventListener("unhandledrejection",this.errorHandler.bind(this))},t.prototype.errorHandler=function(t,n){var r,e;if(this.updateWhiteStatus(),t&&this.isWhite){var o={tags:{white_screen_about:"white"},level:"warn"};t instanceof Error?this.errors.push(p({type:u+(n||"")+t.message,stack:t.stack||""},o)):t instanceof ErrorEvent?this.errors.push(p({type:u+(n||"")+t.message,stack:(null===(r=null==t?void 0:t.error)||void 0===r?void 0:r.stack)||t.error},o)):t instanceof PromiseRejectionEvent&&this.errors.push(p({type:u+(n||"")+t.type,stack:(null===(e=null==t?void 0:t.reason)||void 0===e?void 0:e.stack)||t.reason},o))}},t.prototype.vueErrorHandler=function(t){this.errorHandler(t,"【vue】")},t.prototype._upload=function(){var t,n,r=this;this.isUpload||(this._gdOwl.logService.log("upload gdOwl"),this.isUpload=!0,this.updateWhiteStatus(),this.isWhite&&this.errors.length>0?this._gdOwl.addCustomMetric("white_screen",{gdId:null===(t=null===window||void 0===window?void 0:window.globalData)||void 0===t?void 0:t.gdId}):this._gdOwl.addCustomMetric("not_white_screen",{gdId:null===(n=null===window||void 0===window?void 0:window.globalData)||void 0===n?void 0:n.gdId}),this.errors.forEach((function(t){r._gdOwl.addCustomError(t)})),this._gdOwl.originOwl.sendErrors(),this._gdOwl.metricInst.report())},t.prototype.updateWhiteStatus=function(){this.isWhite=this._domCheck()},t.prototype._domCheck=function(){var t=document.getElementById("app");return!(!t||!t.children)&&t.children.length<=0},t}(),h=function(t,n){return{name:t,value:void 0===n?-1:n,delta:0,entries:[],id:"v2-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)}},w=function(t,n){var r=function r(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||(t(e),n&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0)},g=function(t){addEventListener("pageshow",(function(n){n.persisted&&t(n)}),!0)},y=function(t,n,r){var e;return function(o){n.value>=0&&(o||r)&&(n.delta=n.value-(e||0),(n.delta||void 0===e)&&(e=n.value,t(n)))}},m=-1,O=function(){return"hidden"===document.visibilityState?0:1/0},b=function(){w((function(t){var n=t.timeStamp;m=n}),!0)},x=function(){return m<0&&(m=O(),b(),g((function(){setTimeout((function(){m=O(),b()}),0)}))),{get firstHiddenTime(){return m}}},_=(new Date,{}),S=function(){function t(t){this._init(t),this._owl=t}return t.prototype._init=function(t){this.nextTick((function(){!function(t,n){var r,e=x(),o=h("LCP"),i=function(t){var n=t.startTime;n<e.firstHiddenTime&&(o.value=n,o.entries.push(t),r())},c=function(t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){if("first-input"===t&&!("PerformanceEventTiming"in self))return;var r=new PerformanceObserver((function(t){return t.getEntries().map(n)}));return r.observe({type:t,buffered:!0}),r}}catch(t){}}("largest-contentful-paint",i);if(c){r=y(t,o,n);var a=function(){_[o.id]||(c.takeRecords().map(i),c.disconnect(),_[o.id]=!0,r(!0))};["keydown","click"].forEach((function(t){addEventListener(t,a,{once:!0,capture:!0})})),w(a,!0),g((function(e){o=h("LCP"),r=y(t,o,n),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-e.timeStamp,_[o.id]=!0,r(!0)}))}))}))}}((function(n){t.addCustomPoint({position:5,duration:null==n?void 0:n.value,timeStamp:Date.now()})}))}))},t.prototype.nextTick=function(t){var n=this;new Promise((function(t){return t(!0)})).then((function(){t()})).catch((function(t){n._owl.logService.log(JSON.stringify(t))}))},t}(),E=S,j=function(){return j=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++)for(var o in n=arguments[r])Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o]);return t},j.apply(this,arguments)},P=function(){function t(){var t,n=this;this.__version__="0.0.13",this.originOwl=window.Owl,this.metricInst=this.originOwl.MetricManager(),this.logService=o(),this.reportTpl=(t=this,function(){var n,r,e,o=window.gdTplVersion,i=!!window.enlight;o&&(o.indexOf("api")>-1||o.indexOf("enlight")>-1?i?t.addCustomMetric("tpl_enlight",{gdId:null===(n=null===window||void 0===window?void 0:window.globalData)||void 0===n?void 0:n.gdId}):t.addCustomMetric("tpl_single",{gdId:null===(r=null===window||void 0===window?void 0:window.globalData)||void 0===r?void 0:r.gdId}):o.indexOf("ftl")>-1?t.addCustomMetric("tpl_ftl",{gdId:null===(e=null===window||void 0===window?void 0:window.globalData)||void 0===e?void 0:e.gdId}):t.addNormalError(new Error("tpl undefined"),"【gdOwl】"))}),this.indicatorService=new E(this),this.whiteScreenService=new v(this,"app"),this._injectVueError=this.whiteScreenService.vueErrorHandler.bind(this.whiteScreenService),this.ajaxSampleApi=.3,this.gdOwlProject="";var r=!!window.enlight;this.originOwl[r?"config":"start"]=function(t,n,r,e){var o=t&&t[n];return"function"!=typeof o?a:function(){for(var n=[],c=0;c<arguments.length;c++)n[c]=arguments[c];i((function(){r.apply(void 0,n)}),(function(){o.call.apply(o,function(t,n,r){if(r||2===arguments.length)for(var e,o=0,i=n.length;o<i;o++)!e&&o in n||(e||(e=Array.prototype.slice.call(n,0,o)),e[o]=n[o]);return t.concat(e||Array.prototype.slice.call(n))}([t],n,!1))})),i(e)}}(this.originOwl,r?"config":"start",(function(t){i(n._changeOwlProject.bind(n)),t.project=n.gdOwlProject,t.onBatchPush=n._onBatchPush.bind(n),t.onErrorPush=n._onErrorPush(t.onErrorPush),t.error=j(j({},t.error),{formatUnhandledRejection:!0}),t.resource=j(j({},t.resource),{enableStatusCheck:!0})}),(function(){n._init()}))}return t.prototype._onErrorPush=function(t){return function(n){var r=n.resourceUrl,e=n.sec_category,o=n.content;if(!("[ajax.fetch] axios.reject"===e&&o&&"string"==typeof o.errorMessage&&o.errorMessage.indexOf("Network Error")>-1))return"Script error."===e&&""===r&&(n.level="warn"),t&&"function"==typeof t?t(n):n}},t.prototype._onBatchPush=function(t){try{var n=t.resourceUrl,r=t.statusCode,e=t.logContent;if(e&&(e.indexOf("from: xhr error")>-1||e.indexOf("from: xhr abort")>-1))return!1;var o=Object.keys(f).filter((function(t){return f[t].reg.test(n)}));if(!o||o.length<=0)return!0;var i=f[o[0]],c=r.split("|");return c[0]&&!d.SUCCESS.test(c[0])?this.addCustomMetric(i.fail,{url:n,status:c[0]}):this.addCustomMetric(i.success),!1}catch(t){return this.addCustomError({type:"【_onBatchPush】:".concat(t.message||""),stack:t.stack||"",level:"warn"}),!0}},t.prototype._changeOwlProject=function(){var t=window.globalData.group,n=window.globalData.env.isTest?s:l,r=Object.keys(n).filter((function(r){return n[r].indexOf(parseInt(t,10))>=0}));r.length>0?this.gdOwlProject="com.sankuai.gundam.monitor."+r[0]:this.gdOwlProject=window.globalData.metadata.owlProjectName},t.prototype._init=function(){this.reportTpl()},t.prototype.addCustomMetric=function(t,n,r){var e={};"[object Object]"!==Object.prototype.toString.call(n)?e.type=n:e=n;try{this.metricInst.setTags(e),this.metricInst.setMetric(t,r||1)}catch(t){this.addNormalError(t,"[metricInst.setMetric.error]")}},t.prototype.setMetricExtra=function(t){try{this.metricInst.setExtraData(t)}catch(t){this.addNormalError(t,"[metricInst.setExtraData.error]")}},t.prototype.addNormalError=function(t,n){if(t)try{n&&(n+="_"),this.originOwl.addError({name:"".concat(n).concat(t.message),msg:t.stack},{category:window.Owl.errorModel.CATEGORY.SCRIPT})}catch(t){this.logService.log(JSON.stringify(t))}},t.prototype.addCustomError=function(t){var n=t.type,r=t.stack,e=t.tags,o=t.level;try{this.originOwl.addError({name:n,msg:r},{tags:e,category:window.Owl.errorModel.CATEGORY.SCRIPT,level:o||"error"})}catch(t){this.logService.log(JSON.stringify(t))}},t.prototype.addCustomPoint=function(t){if(t&&"[object Object]"===Object.prototype.toString.call(t))try{this.originOwl.addPoint(t)}catch(t){this.logService.log(JSON.stringify(t))}},t}(),k=P,M=(c=o(),window.Owl&&"function"==typeof window.Owl.start?(window.gdOwl||(window.gdOwl=new k),window.gdOwl):(c.error(new Error("not support")),0))}(),e}()}));
    //# sourceMappingURL=owl-patch.js.map
} catch (e) {
    console.error("【gdOwl error】", e);
}
</script>
<!-- 前端质量监控 梅林 warden sdk 3.1.3-->
<script>
!function(){"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(t,n){var o=window;o._MeiTuanWardenObject=n;var r,i,a,s=function(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e);try{throw new Error(e)}catch(e){}};r=document,i=r.getElementsByTagName("head")[0],(a=r.createElement("script")).defer=a.async=!0,a.charset="utf-8",a.src="//s3plus.meituan.net/v1/mss_3bdfec648fc242aa88aace768b85ae32/warden/3.1.3/warden.min.js",a.crossOrigin="anonymous",i.appendChild(a);if(!o[n]){n=o[n]={};window.addEventListener("DOMContentLoaded",function(){var e=(new Date).getTime();n.tempFCPStampOfServer=e}),window.addEventListener("load",function(){var e=(new Date).getTime();n.tempFCPStampOfFrontEnd=e}),window.addEventListener("load",function(){n.__autoIsWhite&&setTimeout(function(){n.isWhite&&n.isWhite()},1e3)});var u={doms:[],renderType:"frontend"};n.CONFIG=u,n.config=function(t){var o,r;"object"===(void 0===t?"undefined":e(t))?!(o=t).doms||(Array.isArray(o.doms)?o.doms.every(function(t){return"object"===(void 0===t?"undefined":e(t))}):void s("doms必须是数组"))?(Object.keys(t).forEach(function(e){u[e]=t[e]}),n.CONFIG=u,r=n.CONFIG.doms,window.addEventListener("load",function(){r.forEach(function(e){n.isShow&&n.isShow(e.name,e.minNum)})})):s("config传入参数非法"):s("config必须是对象")},n.reportFCPTimeStamp=function(e){void 0===e&&(e=(new Date).getTime()),"number"==typeof e?n.tempFCPStampByReport=e:s("请上传正确的时间戳, number类型")},n.init=n.finish=function(e){var t=(new Date).getTime();n.__ready?(e(n),n.reportT5&&n.reportT5(t)):(n.__inits=n.__inits||[],n.__inits.push(e),n.__inits.push(function(e){e.reportT5&&e.reportT5(t)}))},o.addEventListener("error",function(e){var t=n.reportKeyError;t?t(e):(n.__errs=n.__errs||[],n.__errs.push(e))},!0),n.__autoIsWhite=!1,n.__autoReportWhite=!1,n.removeAutoIsWhite=function(){n.__autoIsWhite=!1},n.openAutoIsWhite=function(){n.__autoIsWhite=!0},n.openAutoReportWhite=function(){n.__autoReportWhite=!0}}}(0,"warden")}();
</script>

<script>
 // 高达平台监控
 var pageUrlPrefix = window.gdTplVersion || '';
 if (pageUrlPrefix) {
   pageUrlPrefix += '_';
 }
 pageUrlPrefix += window.globalData.group;
 var owlConfig = window.globalData.config && window.globalData.config.owl || {};
 var maxCount = owlConfig.maxErrCount || 20;
 var maxTime = 60 * 1000 * (owlConfig.maxErrTime || 1);
 var logNameCache = {};
 window.Owl.start({
   project: window.globalData.metadata.owlProjectName,
   devMode: !window.globalData.env.isProd,
   pageUrl: 'GDID_' + window.globalData.gdId,
   resource: {
     sampleApi: window.gdOwl && window.gdOwl.ajaxSampleApi || 0.1,
   },
   page: {
     sample: 0.5,
     auto: true,
     delay: 200,
     disableSensoryImageIndex: true
   },
   error: {
     sample: 1,
     combo: false,
   },
   onErrorPush: function(errInstance) {
        if(!owlConfig.limitSwitch) {
            return errInstance;
        }
        var name = errInstance && errInstance.sec_category;
        if(name === 'unhandledrejection') {
        	errInstance.level = 'warn'
        }
        var cache = logNameCache[name] || {
            time: Date.now(),
            count: 0
        };
        if((Date.now() - cache.time <= maxTime) && cache.count < maxCount) {
            cache.count ++;
            logNameCache[name] = cache;
            return errInstance;
        }
        if((Date.now() - cache.time) > maxTime) {
            logNameCache[name].time = Date.now();
            logNameCache[name].count = 1;
            return errInstance;
        }
        return undefined;
   },
   noScriptError: false,
   logan:{
     enable: true,
     Logan: Logan.config({
       customReport: {
         biz: 'gd-core-lib',
       },
       disableShake: true
     })
   },
   autoCatch: {
      // fetch收集
      fetch: true,
   }
 });
 if(window.globalData.env.isWxMp) {
    window.Owl.setDimension({
        container: 'miniProgram'
    });
 }
</script>



<!--正常资源脚本-->
<!-- 基础库（必需部分-开始）-->
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181107-v1-axios-0.18.0.js"></script>
<!-- 基础库（必需部分-结束）-->

<!-- 基础库（灰度部分-开始）-->
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20231013-2.5.17-vue.runtime.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181107-v1-vuex-3.0.1.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181107-v1-vue-router-3.0.1.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181127-v1-fastclick-1.0.6.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181107-v1-babel-polyfill-6.26.0.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20181107-v1-intersection-observer-0.5.1.js"></script>
<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_e6aa2b2c35b3432988a7a61f7ed79d37/h5guard/H5guard.js"></script>

<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/gundam-20250619-1.14.42-core.js"></script>

<!-- 基础库（灰度部分-结束）-->

<script crossorigin="anonymous" type="text/javascript" src="https://s3plus.meituan.net/v1/mss_91f3b645703642ce914d9ce3610eaf4c/gundampage/app.17517199600208ff293db616759ea50806f8b5461153d.js"></script>


<!-- 预览水印 -->
<!-- 预览水印 -->

<style>
 .toast-crew .alertBox {position: fixed; left: 0; top: 50%; display: flex; justify-content: center; width: 100%; transform: translateY(-50%);z-index: 10000;}
</style>

</body>

</html>
