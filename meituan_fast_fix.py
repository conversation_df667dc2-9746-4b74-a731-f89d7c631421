# 美团 render 参数修改脚本 - 高性能版本

from reqable import *

def onResponse(context, response):
    # 第一层过滤：快速放行非美团域名
    if 'meituan.com' not in context.url:
        return response

    # 第二层过滤：快速放行空响应
    if not response.body:
        return response
    
    # 第三层过滤：快速检查是否包含目标字符串
    body = response.body
    if '"render":false' not in body and '"render": false' not in body:
        return response

    # 只有确实需要修改的响应才进行处理
    try:
        response.body = body.replace('"render":false', '"render":true').replace('"render": false', '"render": true')
    except:
        pass  # 静默处理错误，避免影响其他请求
    
    return response
