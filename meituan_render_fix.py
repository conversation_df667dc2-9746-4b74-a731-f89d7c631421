# API Docs: https://reqable.com/docs/capture/addons

from reqable import *

def onRequest(context, request):
    # 只打印美团域名的请求
    if 'meituan.com' in context.url:
        print('美团请求 URL: ' + context.url)
    return request

def onResponse(context, response):
    # 只处理美团域名的响应
    if 'meituan.com' not in context.url:
        return response

    print(f'处理美团响应: {context.url}')

    # 检查响应体是否存在
    if not response.body:
        return response

    # 获取 content-type，兼容不同的 HttpHeaders 实现
    def get_content_type(headers):
        content_type = ''
        try:
            # 尝试不同的方式获取 content-type
            if hasattr(headers, 'get'):
                content_type = headers.get('content-type', '') or headers.get('Content-Type', '')
            elif hasattr(headers, '__getitem__'):
                try:
                    content_type = headers['content-type']
                except KeyError:
                    try:
                        content_type = headers['Content-Type']
                    except KeyError:
                        content_type = ''
            else:
                # 如果都不行，尝试转换为字符串查找
                headers_str = str(headers).lower()
                if 'content-type' in headers_str:
                    if 'text/html' in headers_str:
                        content_type = 'text/html'
                    elif 'application/json' in headers_str:
                        content_type = 'application/json'
                    elif 'text/javascript' in headers_str:
                        content_type = 'text/javascript'
        except Exception as e:
            print(f'获取 content-type 失败: {e}')
            content_type = ''

        return content_type.lower() if content_type else ''

    try:
        content_type = get_content_type(response.headers)
        
        # 如果响应是 HTML 格式
        if 'text/html' in content_type:
            print('处理 HTML 响应')
            original_body = response.body
            modified_body = original_body.replace('"render":false', '"render":true')
            modified_body = modified_body.replace('"render": false', '"render": true')
            
            if original_body != modified_body:
                response.body = modified_body
                changes = original_body.count('"render":false') + original_body.count('"render": false')
                print(f'HTML中修改了 {changes} 处 render 参数')
        
        # 如果响应是 JSON 格式
        elif 'application/json' in content_type:
            print('处理 JSON 响应')
            try:
                import json
                data = json.loads(response.body)
                
                # 递归修改所有 render 字段
                def modify_render(obj):
                    changes = 0
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            if key == 'render' and value is False:
                                obj[key] = True
                                changes += 1
                            elif isinstance(value, (dict, list)):
                                changes += modify_render(value)
                    elif isinstance(obj, list):
                        for item in obj:
                            changes += modify_render(item)
                    return changes
                
                changes = modify_render(data)
                if changes > 0:
                    response.body = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                    print(f'JSON中修改了 {changes} 处 render 参数')
                    
            except Exception as e:
                print(f'JSON解析失败，使用字符串替换: {e}')
                original_body = response.body
                modified_body = original_body.replace('"render":false', '"render":true')
                if original_body != modified_body:
                    response.body = modified_body
                    print('使用字符串替换完成')
        
        # 处理其他文本格式（如 JavaScript）
        elif 'text/' in content_type:
            print(f'处理文本响应: {content_type}')
            original_body = response.body
            modified_body = original_body.replace('"render":false', '"render":true')
            modified_body = modified_body.replace('"render": false', '"render": true')
            
            if original_body != modified_body:
                response.body = modified_body
                changes = original_body.count('"render":false') + original_body.count('"render": false')
                print(f'文本中修改了 {changes} 处 render 参数')
                
    except Exception as e:
        print(f'处理响应时出错: {e}')
    
    return response
