# API Docs: https://reqable.com/docs/capture/addons

from reqable import *

def onRequest(context, request):
    # 只打印美团域名的请求
    if 'meituan.com' in context.url:
        print('美团请求 URL: ' + context.url)
    return request

def onResponse(context, response):
    # 只处理美团域名的响应
    if 'meituan.com' not in context.url:
        return response
    
    print(f'处理美团响应: {context.url}')
    
    # 检查响应体是否存在
    if not response.body:
        print('响应体为空，跳过处理')
        return response
    
    try:
        # 简化处理：直接对响应体进行字符串替换，不依赖 content-type
        original_body = response.body
        
        # 执行替换操作
        modified_body = original_body.replace('"render":false', '"render":true')
        modified_body = modified_body.replace('"render": false', '"render": true')
        modified_body = modified_body.replace("'render':false", "'render':true")
        modified_body = modified_body.replace("'render': false", "'render': true")
        
        # 检查是否有修改
        if original_body != modified_body:
            response.body = modified_body
            
            # 计算修改次数
            changes = (
                original_body.count('"render":false') + 
                original_body.count('"render": false') +
                original_body.count("'render':false") +
                original_body.count("'render': false")
            )
            
            print(f'成功修改了 {changes} 处 render 参数')
        else:
            print('未发现需要修改的 render 参数')
            
    except Exception as e:
        print(f'处理响应时出错: {e}')
        # 即使出错也尝试基本的字符串替换
        try:
            if response.body and '"render":false' in response.body:
                response.body = response.body.replace('"render":false', '"render":true')
                print('使用备用方法完成替换')
        except:
            print('备用方法也失败了')
    
    return response
